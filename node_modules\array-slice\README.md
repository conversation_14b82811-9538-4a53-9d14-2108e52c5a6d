# array-slice [![NPM version](https://img.shields.io/npm/v/array-slice.svg?style=flat)](https://www.npmjs.com/package/array-slice) [![NPM monthly downloads](https://img.shields.io/npm/dm/array-slice.svg?style=flat)](https://npmjs.org/package/array-slice) [![NPM total downloads](https://img.shields.io/npm/dt/array-slice.svg?style=flat)](https://npmjs.org/package/array-slice) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/array-slice.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/array-slice)

> Array-slice method. Slices `array` from the `start` index up to, but not including, the `end` index.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save array-slice
```

This function is used instead of `Array#slice` to support node lists in IE < 9 and to ensure dense arrays are returned. This is also faster than native slice in some cases.

## Usage

```js
var slice = require('array-slice');
var arr = ['a', 'b', 'd', 'e', 'f', 'g', 'h', 'i', 'j'];

slice(arr, 3, 6);
//=> ['e', 'f', 'g']
```

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [arr-flatten](https://www.npmjs.com/package/arr-flatten): Recursively flatten an array or arrays. | [homepage](https://github.com/jonschlinkert/arr-flatten "Recursively flatten an array or arrays.")
* [array-unique](https://www.npmjs.com/package/array-unique): Remove duplicate values from an array. Fastest ES5 implementation. | [homepage](https://github.com/jonschlinkert/array-unique "Remove duplicate values from an array. Fastest ES5 implementation.")
* [array-xor](https://www.npmjs.com/package/array-xor): Returns the symmetric difference (exclusive-or) of an array of elements (elements that are present in… [more](https://github.com/jonschlinkert/array-xor) | [homepage](https://github.com/jonschlinkert/array-xor "Returns the symmetric difference (exclusive-or) of an array of elements (elements that are present in all given arrays and not in their intersections).")

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on November 30, 2017._