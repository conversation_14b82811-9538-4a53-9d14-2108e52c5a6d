import { MCFSerializer, MCFCGenerator } from "./mcf.ts";
import * as fs from "node:fs";

// Demo data structures similar to your game objects
const itemData = {
    name: "diamond_sword",
    properties: {
        fire_resistant: false,
        max_stack: 1,
        tier: 3,
        durability: 1561,
        modifiera: 8,
        modifierb: 1
    },
    behaviour: { name: "weapon" },
    attachedComponents: ["sharpness", "unbreaking"]
};

const blockData = {
    name: "oak_log",
    properties: {
        hardness: 2.0,
        resistance: 2.0,
        luminance: 0,
        transparent: false,
        mineableWith: "axe"
    },
    attachedComponents: ["pillar_block"]
};

const recipeData = {
    name: "oak_planks",
    Rtype: "crafting_shapeless",
    ingredients: ["oak_log"],
    result: "oak_planks",
    count: 4
};

const complexRecipeData = {
    name: "stick",
    Rtype: "crafting_shaped",
    shape: ["S", "S"],
    key: { "S": "oak_planks" },
    result: "stick",
    count: 4
};

function demonstrateMCF() {
    console.log("=== MCF (MineCraft Format) Demonstration ===\n");
    
    const testCases = [
        { name: "Item (Diamond Sword)", data: itemData },
        { name: "Block (Oak Log)", data: blockData },
        { name: "Recipe (Oak Planks)", data: recipeData },
        { name: "Complex Recipe (Stick)", data: complexRecipeData }
    ];
    
    let totalJsonSize = 0;
    let totalMcfSize = 0;
    
    testCases.forEach(testCase => {
        console.log(`--- ${testCase.name} ---`);
        
        const result = MCFSerializer.getCompressionRatio(testCase.data);
        
        console.log(`JSON (${result.json.length} chars):`);
        console.log(result.json);
        console.log(`\nMCF (${result.mcf.length} chars):`);
        console.log(result.mcf);
        console.log(`\nSize reduction: ${(result.ratio * 100).toFixed(1)}%`);
        console.log(`Compression ratio: ${(result.json.length / result.mcf.length).toFixed(2)}:1\n`);
        
        // Test round-trip conversion
        const deserialized = MCFSerializer.deserialize(result.mcf);
        const reserializedJson = JSON.stringify(deserialized);
        const originalJson = JSON.stringify(testCase.data);
        
        if (reserializedJson === originalJson) {
            console.log("✅ Round-trip conversion successful");
        } else {
            console.log("❌ Round-trip conversion failed");
            console.log("Original:", originalJson);
            console.log("Deserialized:", reserializedJson);
        }
        
        totalJsonSize += result.json.length;
        totalMcfSize += result.mcf.length;
        
        console.log("\n" + "=".repeat(50) + "\n");
    });
    
    console.log("=== Overall Statistics ===");
    console.log(`Total JSON size: ${totalJsonSize} characters`);
    console.log(`Total MCF size: ${totalMcfSize} characters`);
    console.log(`Overall size reduction: ${((totalJsonSize - totalMcfSize) / totalJsonSize * 100).toFixed(1)}%`);
    console.log(`Overall compression ratio: ${(totalJsonSize / totalMcfSize).toFixed(2)}:1`);
}

function generateCFiles() {
    console.log("\n=== Generating C Parser Files ===");
    
    // Generate header file
    const headerContent = MCFCGenerator.generateHeader();
    fs.writeFileSync("dist/mcf_parser.h", headerContent);
    console.log("Generated: dist/mcf_parser.h");
    
    // Generate implementation file
    const implContent = MCFCGenerator.generateImplementation();
    fs.writeFileSync("dist/mcf_parser.c", implContent);
    console.log("Generated: dist/mcf_parser.c");
    
    // Generate example C usage
    const exampleC = `
#include "mcf_parser.h"

int main() {
    // Example MCF data from your game
    const char* mcf_data = "n:diamond_sword|p:fr:0;ms:1;t:3;d:1561;ma:8;mb:1|b:n:weapon|ac:sharpness;unbreaking";
    
    // Parse the MCF data
    mcf_object_t* obj = mcf_parse(mcf_data);
    if (!obj) {
        printf("Failed to parse MCF data\\n");
        return 1;
    }
    
    // Access values
    const char* name = mcf_get(obj, "name");
    int max_stack = mcf_get_int(obj, "max_stack", 64);
    int fire_resistant = mcf_get_bool(obj, "fire_resistant", 0);
    
    printf("Item: %s\\n", name ? name : "unknown");
    printf("Max Stack: %d\\n", max_stack);
    printf("Fire Resistant: %s\\n", fire_resistant ? "yes" : "no");
    
    // Clean up
    mcf_free(obj);
    return 0;
}
    `.trim();
    
    fs.writeFileSync("dist/mcf_example.c", exampleC);
    console.log("Generated: dist/mcf_example.c");
    
    // Generate Makefile
    const makefile = `
CC=gcc
CFLAGS=-Wall -Wextra -std=c99

all: mcf_example

mcf_example: mcf_example.c mcf_parser.c mcf_parser.h
\t$(CC) $(CFLAGS) -o mcf_example mcf_example.c mcf_parser.c

clean:
\trm -f mcf_example

.PHONY: all clean
    `.trim();
    
    fs.writeFileSync("dist/Makefile", makefile);
    console.log("Generated: dist/Makefile");
}

function showFormatComparison() {
    console.log("\n=== Format Comparison ===");
    console.log("JSON vs MCF for common game data:\n");

    const examples = [
        {
            description: "Simple item properties",
            json: '{"fire_resistant":false,"max_stack":64,"tier":1}',
            mcf: '{fire_resistant:0|max_stack:64|tier:1}'
        },
        {
            description: "Item with components",
            json: '{"name":"sword","attachedComponents":["sharpness","unbreaking"]}',
            mcf: '{name:sword|attachedComponents:sharpness;unbreaking}'
        },
        {
            description: "Recipe data",
            json: '{"name":"planks","ingredients":["log"],"result":"planks","count":4}',
            mcf: '{name:planks|ingredients:log|result:planks|count:4}'
        }
    ];

    examples.forEach(example => {
        const jsonLen = example.json.length;
        const mcfLen = example.mcf.length;
        const reduction = ((jsonLen - mcfLen) / jsonLen * 100).toFixed(1);

        console.log(`${example.description}:`);
        console.log(`  JSON: ${example.json} (${jsonLen} chars)`);
        console.log(`  MCF:  ${example.mcf} (${mcfLen} chars)`);
        console.log(`  Reduction: ${reduction}%\n`);
    });
}

// Run the demonstration
if (import.meta.main) {
    demonstrateMCF();
    showFormatComparison();
    
    // Ensure dist directory exists
    if (!fs.existsSync("dist")) {
        fs.mkdirSync("dist");
    }
    
    generateCFiles();
    
    console.log("\n=== MCF Format Benefits ===");
    console.log("✅ Size Efficient: 20-40% smaller than JSON");
    console.log("✅ Human Readable: Clear structure with brackets and meaningful separators");
    console.log("✅ C Compatible: Simple parsing with standard string functions");
    console.log("✅ Type Aware: Preserves numbers, booleans, and arrays");
    console.log("✅ Full Field Names: No cryptic abbreviations, easy to understand");
    console.log("✅ Fast: Minimal parsing overhead compared to JSON");
}
