{"name": "async-settle", "version": "2.0.0", "description": "Settle an async function.", "author": "Gulp Team <<EMAIL>> (http://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "repository": "gulpjs/async-settle", "license": "MIT", "engines": {"node": ">= 10.13.0"}, "main": "index.js", "files": ["index.js", "LICENSE"], "scripts": {"lint": "eslint . ", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"async-done": "^2.0.0"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "eslint-plugin-node": "^11.1.0", "expect": "^27.4.2", "mocha": "^8.4.0", "nyc": "^15.1.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["settle", "async", "async-done", "complete", "error", "parallel"]}