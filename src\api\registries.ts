import * as core from "./core";

export abstract class registry<T extends (core.gameComponet<U> | core.gameComponet<U>[]), U extends {name:string}>{
    static all: Record<string, registry<any, {name:string}>[]> = {};
    protected registry: Record<string, T> = {};
    constructor(public readonly type:string, protected space:string){
        if(registry.all[this.type] == undefined){
            registry.all[this.type] = [];
        }
        registry.all[this.type].push(this);
    }
    public get(what:string):T{
        return this.registry[core.key([this.space, what])] as T;
    }
    public add(v:(T extends T[] ? T[number]:T)):T extends Array<T> ? T[0]:T{
        let value = v
        //@ts-ignore
        this.registry[core.key([this.space, value.name as string])] = value;
        (value as core.gameComponet<U>).registryName = core.key([this.space, (value as core.gameComponet<U>).name as string]);
        return value;
    }
    public toArray():U[]|Record<string, U[]>{
        let a:U[] = [];
        Array.from(Object.keys(this.registry)).forEach((value, index)=>{
            a[index] = (this.registry[value] as core.gameComponet<U>).ToJSON() as U;
            a[index].name = this.space + ":" + a[index].name;
        })
        return a
    }
}