import * as api$recipe from "../../src/api/recipe.ts";
import * as api$item from "../../src/api/item.ts";
import * as items from "./items.ts";
//@tsBind-require ./items.ts
import item = api$item
import recipe = api$recipe.recipe;
export type craftingShaplessData = {
    ingredients: item.Item<any>[]&{length:1|2|3|4|5|6|7|8|9};
    result: item.Item<any>;
    count:number;
}
export class craftingShapless extends recipe<craftingShaplessData>{
    Rtype = "crafting_shapeless";
    constructor(name:string, data:craftingShaplessData){
        super(name, data);
    }
}
export type craftingShapedData = {
    shape:string[]&{length:1|2|3};
    key: Record<string, item.Item<any>>;
    result: item.Item<any>;
    count:number;
}
export class craftingShaped extends recipe<craftingShapedData>{
    Rtype = "crafting_shaped";
    constructor(name:string, data:craftingShapedData){
        super(name, data);
    }
}

const recipeReg = new api$recipe.recipeRegistery("minecraft");
export let oak_planks = recipeReg.add([new craftingShapless("oak_planks", {
    ingredients: [items.oakLog],
    result: items.oakPlanks,
    count: 4
})])
export let stick = recipeReg.add([new craftingShaped("stick", {
    shape: [
        "S",
        "S"
    ],
    key: {
        "S": items.oakPlanks
    },
    result: items.stick,
    count: 4
})])