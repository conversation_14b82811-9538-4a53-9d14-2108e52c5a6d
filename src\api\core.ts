import * as registries from "./registries.js";


export let toGo:Record<string, any> = {}
export function send(name:string, what:{}){
    toGo[name] = what
}
export abstract class gameComponet<RecordShaping extends {name:string}>{
    public static empty:gameComponet<any>;
    public abstract ToJSON():RecordShaping|Record<string, RecordShaping[]>;
    public abstract ToMCF():string;
    public registryName:string = "";
    public name:string = "";
}
class X extends gameComponet<{name:string}>{
    public ToJSON():{name:string}{
        return {name:this.name};
    }
    public ToMCF():string{
        return `n:${this.name}`;
    }
}
export function key(a:[string, string]):string{
    return a.join(":");
}
export class RegistryFunc{
    public static all: (()=>Promise<null>)[][] = [];
    constructor(public func:()=>Promise<null>, priority:number){
        if(RegistryFunc.all[priority] == undefined){
            RegistryFunc.all[priority] = [];
        }
        RegistryFunc.all[priority].push(func);
    }
    static async run(){
        let promised:Promise<null>[] = []
        RegistryFunc.all.forEach(async(value)=>{
            value.forEach((value)=>{
                promised.push(value());
            })
            await Promise.all(promised);
            promised = [];
        })
        await Promise.all(promised);
        let all = registries.registry.all;
        let ou:Record<string, any> = {}
        Object.keys(all).forEach((val)=>{
            all[val].forEach((value)=>{
                let a:{name:string}[]|Record<string, {name:string}[]> = value.toArray();
                if(Array.isArray(a)){
                    a.forEach((value)=>{
                        if(ou[val] == undefined){
                            ou[val] = [];
                        }
                        ou[val].push(value);
                    })
                }else{
                    Object.keys(a).forEach((value:string)=>{
                        a[value].forEach((v)=>{
                            if(ou[val] == undefined){
                                ou[val] = {};
                            }
                            if(ou[val][value] == undefined){
                                ou[val][value] = [];
                            }
                            ou[val][value].push(v);
                        })
                    })
                }
            })
        })
        return ou
    }
}
gameComponet.empty = new X();
type NonFunctionKeys<T> = {
    [K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];
export type NonFunctionProperties<T> = Pick<T, NonFunctionKeys<T>>