import * as api$core from "../../src/api/core.ts";
export const DyeColor = {
    red:       ["#ff0000", "R" ],
    yellow:    ["#ffff00", "Y" ],
    blue:      ["#0000ff", "B" ],
    orange:    ["#cc8800", "RY"],
    purple:    ["#880088", "RB"],
    green:     ["#008800", "YB"],
    white:     ["#eeeeee", "W" ],
    black:     ["#111111", "K" ],
    pink:      ["#ff55cc", "RW"],
    l_blue:    ["#9999ff", "BW"],
    salmon:    ["#ffaa77", "WWRY"],
    magenta:   ["#ff00ff", "WRB"],
    lime:      ["#00ff00", "WWYB"],
    brown:     ["#663300", "RYB"],
    tan:       ["#996644", "WWWRYB"],
    marroon:   ["#662222", "RRRRYBKK<PERSON>"],
    navy:      ["#000033", "BKK"],
    deep_blue: ["#000077", "BK"],
    forest:    ["#004400", "KKYB"],
    d_forest:  ["#002200", "YBKK"],
    gray:      ["#666666", "WK"],
    l_gray:    ["#aaaaaa", "WWWK"],
    d_gray:    ["#333333", "WKKK"],
    puce:      ["#446600", "WYBYBRYB"],
    cyan:      ["#00ffcc", "WWBBBY"],
    lavender:  ["#ff88ff", "WWRB"],
    rose:      ["#ffbbbb", "RWWW"],
    dusky_rose:["#775555","RWK"],
    lemon:     ["#ccff33", "YYYB"],
    mint:      ["#33ff99", "YBBBWW"],
    sand:      ["#ddaa88", "WWWWWWRYB"],
    scarlet:   ["#aa0000", "RRK"],
    cream:     ["#ffffcc", "W" ],
    duck_blue: ["#2299cc", "BKK"],
    mid_blue:  ["#0066cc", "BK"],
    crimson:   ["#660000", "RRK"],
    burgundy:  ["#4f0e16", "RYB"],
    plum:      ["#440044", "RBRY"],
    beige:     ["#aaaa88", "WY"],
    hot_pink:  ["#ff0088", "WWRYB"],
    olive:     ["#556b2f", "RYBY"],
    eggplant:  ["#330044", "RBK"],
    coffee:    ["#443322", "RKK"],
    steel:     ["#777799", "WWKKB"],
    dark_brown:["#411f0f", "RYK"],
    jade:      ["#006633", "YYB"],
    mustard:   ["#d2b200", "WWRYK"],
    d_yellow:  ["#776600", "YK"]
}as const;
export type DyeColor = typeof DyeColor[keyof typeof DyeColor]
api$core.send("dye", DyeColor)