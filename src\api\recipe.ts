import * as registries from "./registries.ts";
import * as core from "./core.ts";
import * as item from "./item.ts";
export type recipeJSON<T> = {
    name:string;
}&T
export class recipeRegistery extends registries.registry<recipe<any>[], recipeJSON<any>>{
    constructor(space:string){
        super("recipe", space);
    }
    override add(v:recipe<any>[]&{length:1}):recipe<any>[]{
        if(this.registry[v[0].Rtype] == undefined){
            this.registry[v[0].Rtype] = [];
        }
        this.registry[v[0].Rtype].push(v[0]);
        return v;
    }
    override toArray(): Record<string, recipeJSON<any>[]> {
        let a:Record<string, recipeJSON<any>[]> = {};
        let b = this.registry
        Object.keys(b).forEach((value)=>{
            if(a[value] == undefined){
                a[value] = [];
            }
            b[value].forEach((value)=>{
                a[value.Rtype].push(value.ToJSON() as recipeJSON<any>);
            })
            
        })
        return a;
    }
}
export abstract class recipe<U extends Record<string, any>> extends core.gameComponet<recipeJSON<U>>{
    abstract Rtype:string;
    constructor(public name:string, public data:U){
        super();
        this.name = name;
        this.registryName = name;
    }
    private static processData(data:any):any{
        if(data instanceof core.gameComponet){
            return data.registryName;
        }else if(Array.isArray(data)){
            return data.map((value)=>{
                return this.processData(value);
            })
        }else if(typeof data == "object"){
            let a = Object.keys(data)
            let b = {}
            a.forEach((value)=>{
                b[value] = this.processData(data[value]);
            })
            return b;
        }else{
            return data;
        }
    }
    public override ToJSON():recipeJSON<U>{
        let dat:Record<string, any> = recipe.processData({ ...this.data });
        return {
            name:this.name,
            ...(dat as U)
        };
    }
}
