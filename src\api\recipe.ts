import * as registries from "./registries.ts";
import * as core from "./core.ts";
import * as item from "./item.ts";
export type recipeJSON<T> = {
    name:string;
    Rtype:string;
    data:T;
}
export class recipeRegistery extends registries.registry<recipe<any>[], recipeJSON<any>>{
    constructor(space:string){
        super("recipe", space);
    }
    override add(v:recipe<any>[]&{length:1}):recipe<any>[]{
        if(this.registry[v[0].Rtype] == undefined){
            this.registry[v[0].Rtype] = [];
        }
        this.registry[v[0].Rtype].push(v[0]);
        return v;
    }
    override toArray(): Record<string, recipeJSON<any>[]> {
        let a:Record<string, recipeJSON<any>[]> = {};
        let b = this.registry
        Object.keys(b).forEach((value)=>{
            if(a[value] == undefined){
                a[value] = [];
            }
            b[value].forEach((value)=>{
                a[value.Rtype].push(value.ToJSON() as recipeJSON<any>);
            })
            
        })
        return a;
    }
}
export abstract class recipe<U extends Object> extends core.gameComponet<recipeJSON<U>>{
    abstract Rtype:string;
    constructor(public name:string, public data:U){
        super();
        this.name = name;
        this.registryName = name;
    }
    public override ToJSON():recipeJSON<U>{
        let dat = this.data;
        Object.keys(dat).forEach((value)=>{
            if(dat[value] instanceof item.Item){
                dat[value] = dat[value].registryName;
            }else if(Array.isArray(dat[value])){
                dat[value] = dat[value].map((value)=>{
                    if(value instanceof item.Item){
                        return value.registryName;
                    }
                    return value;
                })
            }
        })
        return {
            name:this.name,
            Rtype:this.Rtype,
            data:dat
        };
    }
}
