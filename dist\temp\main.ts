//@tsBind-Generated

import * as fs from 'node:fs';
//@tsBind-require ./api.ts


// ===== src/api/core.ts =====

namespace api.core {
    
    
    export let toGo:Record<string, any> = {}
    export function send(name:string, what:{}){
        toGo[name] = what
    }
    export abstract class gameComponet<RecordShaping extends {name:string}>{
        public static empty:gameComponet<any>;
        public abstract ToJSON():RecordShaping|Record<string, RecordShaping[]>;
        public registryName:string = "";
        public name:string = "";
    }
    class X extends gameComponet<{name:string}>{
        public ToJSON():{name:string}{
            return {name:this.name};
        }
    }
    export function key(a:[string, string]):string{
        return a.join(":");
    }
    export class RegistryFunc{
        public static all: (()=>Promise<null>)[][] = [];
        constructor(public func:()=>Promise<null>, priority:number){
            if(RegistryFunc.all[priority] == undefined){
                RegistryFunc.all[priority] = [];
            }
            RegistryFunc.all[priority].push(func);
        }
        static async run(){
            let promised:Promise<null>[] = []
            RegistryFunc.all.forEach(async(value)=>{
                value.forEach((value)=>{
                    promised.push(value());
                })
                await Promise.all(promised);
                promised = [];
            })
            await Promise.all(promised);
            let all = registries.registry.all;
            let ou:Record<string, any> = {}
            Object.keys(all).forEach((val)=>{
                all[val].forEach((value)=>{
                    let a:{name:string}[]|Record<string, {name:string}[]> = value.toArray();
                    if(Array.isArray(a)){
                        a.forEach((value)=>{
                            if(ou[val] == undefined){
                                ou[val] = [];
                            }
                            ou[val].push(value);
                        })
                    }else{
                        Object.keys(a).forEach((value:string)=>{
                            a[value].forEach((v)=>{
                                if(ou[val] == undefined){
                                    ou[val] = {};
                                }
                                if(ou[val][value] == undefined){
                                    ou[val][value] = [];
                                }
                                ou[val][value].push(v);
                            })
                        })
                    }
                })
            })
            return ou
        }
    }
    gameComponet.empty = new X();
    type NonFunctionKeys<T> = {
        [K in keyof T]: T[K] extends Function ? never : K;
    }[keyof T];
    export type NonFunctionProperties<T> = Pick<T, NonFunctionKeys<T>>}

// ===== src/api/registries.ts =====

namespace api.registries {
    
    export abstract class registry<T extends (core.gameComponet<U> | core.gameComponet<U>[]), U extends {name:string}>{
        static all: Record<string, registry<any, {name:string}>[]> = {};
        protected registry: Record<string, T> = {};
        constructor(public readonly type:string, protected space:string){
            if(registry.all[this.type] == undefined){
                registry.all[this.type] = [];
            }
            registry.all[this.type].push(this);
        }
        public get(what:string):T{
            return this.registry[core.key([this.space, what])] as T;
        }
        public add(v:(T extends T[] ? T[number]:T)):T extends Array<T> ? T[0]:T{
            let value = v
            //@ts-ignore
            this.registry[core.key([this.space, value.name as string])] = value;
            (value as core.gameComponet<U>).registryName = core.key([this.space, (value as core.gameComponet<U>).name as string]);
            return value;
        }
        public toArray():U[]|Record<string, U[]>{
            let a:U[] = [];
            Array.from(Object.keys(this.registry)).forEach((value, index)=>{
                a[index] = (this.registry[value] as core.gameComponet<U>).ToJSON() as U;
                a[index].name = this.space + ":" + a[index].name;
            })
            return a
        }
    }}

// ===== src/api/block.ts =====

namespace api.block {
    
    type point = [number, number, number]&{length:3};
    type box = ([point, point ]&{length:2})
    export class blockProperties{
        public flammable:boolean = false;
        Flamable(){
            this.flammable = true;
            return this;
        }
        public spreadChance:number = 0;
        SpreadChance(a:number){
            this.spreadChance = a;
            return this;
        }
        public burnTime:number = 0;
        BurnTime(a:number){
            this.burnTime = a;
            return this;
        }
        hitbox: box[] = [[[0,0,0],[16,16,16]]]
        Hitbox(a:box[]){
            this.hitbox = a;
            return this;
        }
        public hardness:number = 1;
        Hardness(a:number){
            this.hardness = a;
            return this;
        }
        public resistance:number = 1;
        Resistance(a:number){
            this.resistance = a;
            return this;
        }
        public friction:number = 0.6;
        Friction(a:number){
            this.friction = a;
            return this;
        }
        public luminance:number = 0;
        Luminance(a:number){
            this.luminance = a;
            return this;
        }
        public transparent:boolean = false
        Transparent(a:boolean){
            this.transparent = a;
            return this;
        }
        public mineableWith:string = ""
        MineableWith(a:string){
            this.mineableWith = a;
            return this;
        }
    
    }
    export type blockJSON = {
        properties:core.NonFunctionProperties<blockProperties>;
        name:string;
        attachedComponents:(string|null)[];
    };
    export type blockBehaviour = {name:string, BL?:null};
    export type blockBehabiourProcessor<T extends blockBehaviour> = 
        T extends block.GUI? block<any> : 
        null|core.gameComponet<any>;
    export namespace block{
        export type GUI = {name:"gui"};
        export type block = {name:"block"};
        export type pillarBlock = {name:"pillar_block"};
    }
    export class block<T extends blockBehaviour> extends core.gameComponet<blockJSON>{
        static readonly GUI:blockBehaviour = {name:"gui"};
        static readonly block:block.block = {name:"block"};
        static readonly pillarBlock:block.pillarBlock = {name:"pillar_block"};
        public ToJSON():blockJSON{
        let attachedComponents:string[] = [];
            this.attachedComponents.forEach((value)=>{
                if(value == null){
                    return;
                }
                attachedComponents.push(value.registryName);
            })
            return {
                properties:this.properties,
                name:this.name,
                attachedComponents: attachedComponents
            };
        }
        constructor(name:string, public behaviour:T, public properties:blockProperties, public attachedComponents:blockBehabiourProcessor<T>[]){
            super();
            this.name = name;
            this.registryName = name;
        }
    }
    export class blockRegistry extends registries.registry<block<any>, blockJSON>{
        constructor(space:string){
            super("block", space);
        }
    }
    export let airBlock:block<block.block>;
    export let bedrockBlock:block<block.block>;
    export let f = new core.RegistryFunc(async ()=>{
        console.log("block")
        let blockReg: blockRegistry = new blockRegistry("_");
        airBlock = blockReg.add(new block("_", block.block,new blockProperties().Hitbox([[[0,0,0],[0,0,0]]]), [null]));  
        bedrockBlock = blockReg.add(new block("bedrock", block.block, new blockProperties().Resistance(-1).Hardness(-1), [null]));
        return null
    }, 0);}

// ===== src/api/item.ts =====

namespace api.item {
    export class ItemProperties{
        public fire_resistant:boolean = false;
        FireResistant(){
            this.fire_resistant = true;
            return this;
        }
        public max_stack:number = 64;
        MaxStack(a:number){
            this.max_stack = a;
            return this;
        }
        public teir = 0;
        Teir(a:number){
            this.teir = a;
            return this;
        }
        public durability = 0;
        Durability(a:number){
            this.durability = a;
            this.max_stack = 1;
            return this;
        }
        modifiera = 1;
        ModifierA(a:number){
            this.modifiera = a;
            return this;
        }
        modifierb = 1;
        ModifierB(a:number){
            this.modifierb = a;
            return this;
        }
    }
    export type ItemBehaviour = {name:string,IT?:null};
    export type ItemJSON = {
        properties: core.NonFunctionProperties<ItemProperties>;
        name: string;
        behaviour: ItemBehaviour;
        attachedComponents: (string| null)[];
    }
    export type itemBehaviourProcesor<T extends ItemBehaviour> = 
        T extends {name:"block_item"} ? block.block<any> : 
        null|core.gameComponet<any>;
    export namespace Item{
        export type Item = {name:"item"};
        export type BlockItem = {name:"block_item"};
        export type Pickaxe = {name:"pickaxe"};
    }
    export class Item<V extends ItemBehaviour> extends core.gameComponet<ItemJSON>{
        static readonly Item:ItemBehaviour = {name:"item"};
        static readonly BlockItem:ItemBehaviour = {name:"block_item"};
        static readonly Pickaxe:ItemBehaviour = {name:"pickaxe"};
        public properties: ItemProperties;
        public attachedComponents:itemBehaviourProcesor<ItemBehaviour>[];
        constructor(name:string, public behaviour:V, properties:ItemProperties, attachedComponents:itemBehaviourProcesor<V>[]){
            super();
            if(attachedComponents[0] == null){
                attachedComponents = [core.gameComponet.empty] as itemBehaviourProcesor<V>[];
            }
            this.name = name;
            this.registryName = name;
            this.properties = properties;
            this.attachedComponents = attachedComponents as core.gameComponet<any>[];
        }
        public ToJSON() {
            let attachedComponents:string[] = [];
            this.attachedComponents.forEach((value)=>{
                if(value == null){
                    return;
                }
                attachedComponents.push(value.registryName);
            })
            return {
                name: this.name,
                properties: this.properties,
                behaviour: this.behaviour,
                attachedComponents: attachedComponents
            }
        }
    }
    export class ItemRegistry extends registries.registry<Item<any>, ItemJSON>{
        constructor(space:string){
            super("item", space);
        }
    }
    export let bedrock:Item<Item.BlockItem>;
    export let air:Item<Item.BlockItem>;
    new core.RegistryFunc(async ()=>{
        console.log("item")
        let itemRegistry: ItemRegistry = new ItemRegistry("_");
        bedrock = itemRegistry.add(new Item("bedrock", Item.BlockItem ,new ItemProperties(), [block.bedrockBlock]));  
        air = itemRegistry.add(new Item("_", Item.Item, new ItemProperties().MaxStack(1), [core.gameComponet.empty]));
        return null
    }, 1);}

// ===== src/api/recipe.ts =====

namespace api.recipe {
    export type recipeJSON<T> = {
        name:string;
    }&T
    export class recipeRegistery extends registries.registry<recipe<any>[], recipeJSON<any>>{
        constructor(space:string){
            super("recipe", space);
        }
        override add(v:recipe<any>[]&{length:1}):recipe<any>[]{
            if(this.registry[v[0].Rtype] == undefined){
                this.registry[v[0].Rtype] = [];
            }
            this.registry[v[0].Rtype].push(v[0]);
            return v;
        }
        override toArray(): Record<string, recipeJSON<any>[]> {
            let a:Record<string, recipeJSON<any>[]> = {};
            let b = this.registry
            Object.keys(b).forEach((value)=>{
                if(a[value] == undefined){
                    a[value] = [];
                }
                b[value].forEach((value)=>{
                    a[value.Rtype].push(value.ToJSON() as recipeJSON<any>);
                })
                
            })
            return a;
        }
    }
    export abstract class recipe<U extends Record<string, any>> extends core.gameComponet<recipeJSON<U>>{
        abstract Rtype:string;
        constructor(public name:string, public data:U){
            super();
            this.name = name;
            this.registryName = name;
        }
        private static processData(data:any):any{
            if(data instanceof core.gameComponet){
                return data.registryName;
            }else if(Array.isArray(data)){
                return data.map((value)=>{
                    return this.processData(value);
                })
            }else if(typeof data == "object"){
                let a = Object.keys(data)
                let b = {}
                a.forEach((value)=>{
                    b[value] = this.processData(data[value]);
                })
                return b;
            }else{
                return data;
            }
        }
        public override ToJSON():recipeJSON<U>{
            let dat:Record<string, any> = recipe.processData({ ...this.data });
            return {
                name:this.name,
                ...(dat as U)
            };
        }
    }
}

// ===== minecraft/data/dyeColour.ts =====

namespace minecraft.data.dyeColour {
    export const DyeColor = {
        red:       ["#ff0000", "R" ],
        yellow:    ["#ffff00", "Y" ],
        blue:      ["#0000ff", "B" ],
        orange:    ["#cc8800", "RY"],
        purple:    ["#880088", "RB"],
        green:     ["#008800", "YB"],
        white:     ["#eeeeee", "W" ],
        black:     ["#111111", "K" ],
        pink:      ["#ff55cc", "RW"],
        l_blue:    ["#9999ff", "BW"],
        salmon:    ["#ffaa77", "WWRY"],
        magenta:   ["#ff00ff", "WRB"],
        lime:      ["#00ff00", "WWYB"],
        brown:     ["#663300", "RYB"],
        tan:       ["#996644", "WWWRYB"],
        marroon:   ["#662222", "RRRRYBKKK"],
        navy:      ["#000033", "BKK"],
        deep_blue: ["#000077", "BK"],
        forest:    ["#004400", "KKYB"],
        d_forest:  ["#002200", "YBKK"],
        gray:      ["#666666", "WK"],
        l_gray:    ["#aaaaaa", "WWWK"],
        d_gray:    ["#333333", "WKKK"],
        puce:      ["#446600", "WYBYBRYB"],
        cyan:      ["#00ffcc", "WWBBBY"],
        lavender:  ["#ff88ff", "WWRB"],
        rose:      ["#ffbbbb", "RWWW"],
        dusky_rose:["#775555","RWK"],
        lemon:     ["#ccff33", "YYYB"],
        mint:      ["#33ff99", "YBBBWW"],
        sand:      ["#ddaa88", "WWWWWWRYB"],
        scarlet:   ["#aa0000", "RRK"],
        cream:     ["#ffffcc", "W" ],
        duck_blue: ["#2299cc", "BKK"],
        mid_blue:  ["#0066cc", "BK"],
        crimson:   ["#660000", "RRK"],
        burgundy:  ["#4f0e16", "RYB"],
        plum:      ["#440044", "RBRY"],
        beige:     ["#aaaa88", "WY"],
        hot_pink:  ["#ff0088", "WWRYB"],
        olive:     ["#556b2f", "RYBY"],
        eggplant:  ["#330044", "RBK"],
        coffee:    ["#443322", "RKK"],
        steel:     ["#777799", "WWKKB"],
        dark_brown:["#411f0f", "RYK"],
        jade:      ["#006633", "YYB"],
        mustard:   ["#d2b200", "WWRYK"],
        d_yellow:  ["#776600", "YK"]
    }as const;
    export type DyeColor = typeof DyeColor[keyof typeof DyeColor]
    api.core.send("dye", DyeColor)}

// ===== minecraft/data/blocks.ts =====

namespace minecraft.data.blocks {
    import Block = api.block
    
    let blockReg = new Block.blockRegistry("minecraft");
    
    const logBlock = new Block.blockProperties().Flamable().BurnTime(500).SpreadChance(0.01).Hardness(3)
    const plankBlock = logBlock.Hardness(1)
    
    export let dirt = blockReg.add(new Block.block("dirt",
        Block.block.block, new Block.blockProperties().MineableWith("S"),
    [null]))
    export let oak_log = blockReg.add(new Block.block("oak_log",
        Block.block.pillarBlock, logBlock,
    [null]))
    export let oak_plank = blockReg.add(new Block.block("oak_plank",
        Block.block.block, plankBlock, 
    [null]))
    
    export let stone = blockReg.add(new Block.block("stone", 
        Block.block.block, new Block.blockProperties().MineableWith("P").Hardness(8).Resistance(1.5),
    [null]
    ))
}

// ===== minecraft/data/items.ts =====

namespace minecraft.data.items {
    import Item = api.item
    const itemReg = new Item.ItemRegistry("minecraft")
    export let dirt = itemReg.add(new Item.Item("dirt",
        Item.Item.BlockItem, new Item.ItemProperties(),
    [blocks.dirt]))
    export let stick = itemReg.add(new Item.Item("stick",
        Item.Item.Item, new Item.ItemProperties(),
    [null]))
    export let ironIngot = itemReg.add(new Item.Item("iron_ingot",
        Item.Item.Item, new Item.ItemProperties(),
    [null]))
    export let ironPickaxe = itemReg.add(new Item.Item("iron_pickaxe",
        Item.Item.Pickaxe, new Item.ItemProperties().Durability(512).Teir(3).ModifierA(2),
    [null]))
    export let oakPlanks = itemReg.add(new Item.Item("oak_planks",
        Item.Item.BlockItem, new Item.ItemProperties(),
    [blocks.oak_plank]))
    export let oakLog = itemReg.add(new Item.Item("oak_log",
        Item.Item.BlockItem, new Item.ItemProperties(),
    [blocks.oak_log]))}

// ===== minecraft/data/recipes.ts =====

namespace minecraft.data.recipes {
    import item = api.item
    import recipe = api.recipe.recipe;
    export type craftingShaplessData = {
        ingredients: item.Item<any>[]&{length:1|2|3|4|5|6|7|8|9};
        result: item.Item<any>;
        count:number;
    }
    export class craftingShapless extends recipe<craftingShaplessData>{
        Rtype = "crafting_shapeless";
        constructor(name:string, data:craftingShaplessData){
            super(name, data);
        }
    }
    export type craftingShapedData = {
        shape:string[]&{length:1|2|3};
        key: Record<string, item.Item<any>>;
        result: item.Item<any>;
        count:number;
    }
    export class craftingShaped extends recipe<craftingShapedData>{
        Rtype = "crafting_shaped";
        constructor(name:string, data:craftingShapedData){
            super(name, data);
        }
    }
    
    const recipeReg = new api.recipe.recipeRegistery("minecraft");
    export let oak_planks = recipeReg.add([new craftingShapless("oak_planks", {
        ingredients: [items.oakLog],
        result: items.oakPlanks,
        count: 4
    })])
    export let stick = recipeReg.add([new craftingShaped("stick", {
        shape: [
            "S",
            "S"
        ],
        key: {
            "S": items.oakPlanks
        },
        result: items.stick,
        count: 4
    })])}

// ===== src/api/main.ts =====

namespace api.main {
    (async ()=>{
        let ou = await core.RegistryFunc.run();
        let out:string = JSON.stringify({...ou, ...core.toGo})
        fs.writeFileSync("dist/out.json", out);
    })();}
