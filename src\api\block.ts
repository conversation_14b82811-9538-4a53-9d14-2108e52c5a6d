import * as core from "./core.ts";
import * as registries from "./registries.ts";
import * as mcf$MCFSerializer from "./mcf.ts";
const MCFSerializer = mcf$MCFSerializer.MCFSerializer;
type point = [number, number, number]&{length:3};
type box = ([point, point ]&{length:2})
export class blockProperties{
    public flammable:boolean = false;
    Flamable(){
        this.flammable = true;
        return this;
    }
    public spreadChance:number = 0;
    SpreadChance(a:number){
        this.spreadChance = a;
        return this;
    }
    public burnTime:number = 0;
    BurnTime(a:number){
        this.burnTime = a;
        return this;
    }
    hitbox: box[] = [[[0,0,0],[16,16,16]]]
    Hitbox(a:box[]){
        this.hitbox = a;
        return this;
    }
    public hardness:number = 1;
    Hardness(a:number){
        this.hardness = a;
        return this;
    }
    public resistance:number = 1;
    Resistance(a:number){
        this.resistance = a;
        return this;
    }
    public friction:number = 0.6;
    Friction(a:number){
        this.friction = a;
        return this;
    }
    public luminance:number = 0;
    Luminance(a:number){
        this.luminance = a;
        return this;
    }
    public transparent:boolean = false
    Transparent(a:boolean){
        this.transparent = a;
        return this;
    }
    public mineableWith:string = ""
    MineableWith(a:string){
        this.mineableWith = a;
        return this;
    }

}
export type blockJSON = {
    properties:core.NonFunctionProperties<blockProperties>;
    name:string;
    attachedComponents:(string|null)[];
};
export type blockBehaviour = {name:string, BL?:null};
export type blockBehabiourProcessor<T extends blockBehaviour> = 
    T extends block.GUI? block<any> : 
    null|core.gameComponet<any>;
export namespace block{
    export type GUI = {name:"gui"};
    export type block = {name:"block"};
    export type pillarBlock = {name:"pillar_block"};
}
export class block<T extends blockBehaviour> extends core.gameComponet<blockJSON>{
    static readonly GUI:blockBehaviour = {name:"gui"};
    static readonly block:block.block = {name:"block"};
    static readonly pillarBlock:block.pillarBlock = {name:"pillar_block"};
    public ToJSON():blockJSON{
    let attachedComponents:string[] = [];
        this.attachedComponents.forEach((value)=>{
            if(value == null){
                return;
            }
            attachedComponents.push(value.registryName);
        })
        return {
            properties:this.properties,
            name:this.name,
            attachedComponents: attachedComponents
        };
    }

    public ToMCF():string {
        let attachedComponents:string[] = [];
        this.attachedComponents.forEach((value)=>{
            if(value == null){
                return;
            }
            attachedComponents.push(value.registryName);
        })

        const data = {
            properties: this.properties,
            name: this.name,
            attachedComponents: attachedComponents
        };

        return MCFSerializer.serialize(data);
    }
    constructor(name:string, public behaviour:T, public properties:blockProperties, public attachedComponents:blockBehabiourProcessor<T>[]){
        super();
        this.name = name;
        this.registryName = name;
    }
}
export class blockRegistry extends registries.registry<block<any>, blockJSON>{
    constructor(space:string){
        super("block", space);
    }
}
export let airBlock:block<block.block>;
export let bedrockBlock:block<block.block>;
export let f = new core.RegistryFunc(async ()=>{
    console.log("block")
    let blockReg: blockRegistry = new blockRegistry("_");
    airBlock = blockReg.add(new block("_", block.block,new blockProperties().Hitbox([[[0,0,0],[0,0,0]]]), [null]));  
    bedrockBlock = blockReg.add(new block("bedrock", block.block, new blockProperties().Resistance(-1).Hardness(-1), [null]));
    return null
}, 0);