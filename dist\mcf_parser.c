#include "mcf_parser.h"

// Field mappings
static const struct {
    const char* short_name;
    const char* full_name;
} field_map[] = {
    {"n", "name"},
    {"p", "properties"},
    {"b", "behaviour"},
    {"ac", "attachedComponents"},
    {"fr", "fire_resistant"},
    {"ms", "max_stack"},
    {"t", "tier"},
    {"d", "durability"},
    {"ma", "modifiera"},
    {"mb", "modifierb"},
    {"h", "hardness"},
    {"r", "resistance"},
    {"l", "luminance"},
    {"tr", "transparent"},
    {"mw", "mineableWith"},
    {"i", "ingredients"},
    {"res", "result"},
    {"c", "count"},
    {"s", "shape"},
    {"k", "key"},
    {"rt", "Rtype"},
    {NULL, NULL}
};

const char* mcf_expand_field(const char* short_name) {
    for (int i = 0; field_map[i].short_name != NULL; i++) {
        if (strcmp(field_map[i].short_name, short_name) == 0) {
            return field_map[i].full_name;
        }
    }
    return short_name;
}

mcf_object_t* mcf_parse(const char* mcf_string) {
    if (!mcf_string) return NULL;
    
    mcf_object_t* obj = malloc(sizeof(mcf_object_t));
    obj->pairs = malloc(sizeof(mcf_pair_t) * 16);
    obj->count = 0;
    obj->capacity = 16;
    
    char* str_copy = strdup(mcf_string);
    char* token = strtok(str_copy, "|");
    
    while (token != NULL) {
        char* colon = strchr(token, ':');
        if (colon != NULL) {
            *colon = '\0';
            
            if (obj->count >= obj->capacity) {
                obj->capacity *= 2;
                obj->pairs = realloc(obj->pairs, sizeof(mcf_pair_t) * obj->capacity);
            }
            
            obj->pairs[obj->count].key = strdup(mcf_expand_field(token));
            obj->pairs[obj->count].value = strdup(colon + 1);
            obj->count++;
        }
        token = strtok(NULL, "|");
    }
    
    free(str_copy);
    return obj;
}

const char* mcf_get(mcf_object_t* obj, const char* key) {
    if (!obj || !key) return NULL;
    
    for (int i = 0; i < obj->count; i++) {
        if (strcmp(obj->pairs[i].key, key) == 0) {
            return obj->pairs[i].value;
        }
    }
    return NULL;
}

int mcf_get_int(mcf_object_t* obj, const char* key, int default_value) {
    const char* value = mcf_get(obj, key);
    return value ? atoi(value) : default_value;
}

int mcf_get_bool(mcf_object_t* obj, const char* key, int default_value) {
    const char* value = mcf_get(obj, key);
    if (!value) return default_value;
    return (strcmp(value, "1") == 0) ? 1 : 0;
}

void mcf_free(mcf_object_t* obj) {
    if (!obj) return;
    
    for (int i = 0; i < obj->count; i++) {
        free(obj->pairs[i].key);
        free(obj->pairs[i].value);
    }
    free(obj->pairs);
    free(obj);
}
