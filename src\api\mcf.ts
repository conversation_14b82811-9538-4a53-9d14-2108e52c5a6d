/**
 * MCF (MineCraft Format) - A size-efficient, human-readable, C-compatible serialization format
 * 
 * Format Specification:
 * - Uses pipe (|) as primary delimiter
 * - Uses colon (:) for key-value pairs
 * - Uses semicolon (;) for array elements
 * - Uses short field names to reduce size
 * - Numbers are stored as-is (no quotes)
 * - Booleans as 1/0
 * - Strings are unquoted unless they contain delimiters
 * 
 * Example:
 * JSON: {"name":"oak_planks","properties":{"fire_resistant":false,"max_stack":64}}
 * MCF:  n:oak_planks|p:fr:0;ms:64
 */

export interface MCFSerializable {
    toMCF(): string;
    fromMCF(data: string): void;
}

export class MCFSerializer {
    private static readonly DELIM_MAIN = ';';
    private static readonly DELIM_KV = ':';
    private static readonly DELIM_ARRAY = ',';
    private static readonly DELIM_ESCAPE = '|';
    private static readonly BRACKET_OPEN = '{';
    private static readonly BRACKET_CLOSE = '}';

    /**
     * Serialize any object to MCF format
     */
    static serialize(obj: any): string {
        if (obj === null || obj === undefined) return '';
        if (typeof obj === 'string') return MCFSerializer.escapeString(obj);
        if (typeof obj === 'number') return obj.toString();
        if (typeof obj === 'boolean') return obj ? '1' : '0';
        if (Array.isArray(obj)) {
            return obj.map(item => MCFSerializer.serialize(item)).join(MCFSerializer.DELIM_ARRAY);
        }
        if (typeof obj === 'object') {
            const content = Object.entries(obj)
                .filter(([_, value]) => value !== null && value !== undefined)
                .map(([key, value]) => {
                    const serializedValue = MCFSerializer.serialize(value);
                    return `${key}${MCFSerializer.DELIM_KV}${serializedValue}`;
                })
                .join(MCFSerializer.DELIM_MAIN);
            return `${MCFSerializer.BRACKET_OPEN}${content}${MCFSerializer.BRACKET_CLOSE}`;
        }
        return String(obj);
    }

    /**
     * Deserialize MCF format to object
     */
    static deserialize(mcf: string): any {
        if (!mcf) return null;

        // Check if it's a simple value
        if (!mcf.includes(MCFSerializer.DELIM_MAIN) && !mcf.includes(MCFSerializer.DELIM_KV) &&
            !mcf.startsWith(MCFSerializer.BRACKET_OPEN)) {
            return MCFSerializer.parseValue(mcf);
        }

        // Handle bracketed objects
        if (mcf.startsWith(MCFSerializer.BRACKET_OPEN) && mcf.endsWith(MCFSerializer.BRACKET_CLOSE)) {
            const content = mcf.substring(1, mcf.length - 1);
            if (!content) return {};

            const result: any = {};
            const pairs = MCFSerializer.splitRespectingEscapes(content, MCFSerializer.DELIM_MAIN);

            for (const pair of pairs) {
                const colonIndex = pair.indexOf(MCFSerializer.DELIM_KV);
                if (colonIndex === -1) continue;

                const key = pair.substring(0, colonIndex);
                const valueStr = pair.substring(colonIndex + 1);

                result[key] = MCFSerializer.parseValue(valueStr);
            }

            return result;
        }

        // Handle legacy format without brackets
        const result: any = {};
        const pairs = MCFSerializer.splitRespectingEscapes(mcf, MCFSerializer.DELIM_MAIN);

        for (const pair of pairs) {
            const colonIndex = pair.indexOf(MCFSerializer.DELIM_KV);
            if (colonIndex === -1) continue;

            const key = pair.substring(0, colonIndex);
            const valueStr = pair.substring(colonIndex + 1);

            result[key] = MCFSerializer.parseValue(valueStr);
        }

        return result;
    }

    private static parseValue(value: string): any {
        if (!value) return null;

        // Check for bracketed object
        if (value.startsWith(MCFSerializer.BRACKET_OPEN) && value.endsWith(MCFSerializer.BRACKET_CLOSE)) {
            return MCFSerializer.deserialize(value);
        }

        // Check for array
        if (value.includes(MCFSerializer.DELIM_ARRAY)) {
            return MCFSerializer.splitRespectingEscapes(value, MCFSerializer.DELIM_ARRAY)
                .map(item => MCFSerializer.parseValue(item));
        }

        // Check for nested object (legacy format)
        if (value.includes(MCFSerializer.DELIM_MAIN)) {
            return MCFSerializer.deserialize(value);
        }

        // Parse primitive values
        if (value === '1') return true;
        if (value === '0') return false;

        const num = Number(value);
        if (!isNaN(num) && isFinite(num)) return num;

        return MCFSerializer.unescapeString(value);
    }

    private static escapeString(str: string): string {
        return str
            .replace(/\\/g, '\\\\')
            .replace(/\|/g, '\\|')
            .replace(/:/g, '\\:')
            .replace(/;/g, '\\;')
            .replace(/\{/g, '\\{')
            .replace(/\}/g, '\\}');
    }

    private static unescapeString(str: string): string {
        return str
            .replace(/\\}/g, '}')
            .replace(/\\{/g, '{')
            .replace(/\\;/g, ';')
            .replace(/\\:/g, ':')
            .replace(/\\\|/g, '|')
            .replace(/\\\\/g, '\\');
    }

    private static splitRespectingEscapes(str: string, delimiter: string): string[] {
        const result: string[] = [];
        let current = '';
        let escaped = false;
        
        for (let i = 0; i < str.length; i++) {
            const char = str[i];
            
            if (escaped) {
                current += char;
                escaped = false;
            } else if (char === MCFSerializer.DELIM_ESCAPE) {
                current += char;
                escaped = true;
            } else if (char === delimiter) {
                result.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        
        if (current) result.push(current);
        return result;
    }


}


