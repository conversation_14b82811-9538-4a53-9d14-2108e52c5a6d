/**
 * MCF (MineCraft Format) - A size-efficient, human-readable, C-compatible serialization format
 * 
 * Format Specification:
 * - Uses pipe (|) as primary delimiter
 * - Uses colon (:) for key-value pairs
 * - Uses semicolon (;) for array elements
 * - Uses short field names to reduce size
 * - Numbers are stored as-is (no quotes)
 * - Booleans as 1/0
 * - Strings are unquoted unless they contain delimiters
 * 
 * Example:
 * JSON: {"name":"oak_planks","properties":{"fire_resistant":false,"max_stack":64}}
 * MCF:  n:oak_planks|p:fr:0;ms:64
 */

export interface MCFSerializable {
    toMCF(): string;
    fromMCF(data: string): void;
}
const DELIM_MAIN = ';';
const DELIM_KV = '=';
const DELIM_ARRAY = ',';
const DELIM_ESCAPE = '|';
const BRACKET_OPEN = '{';
const BRACKET_CLOSE = '}';
const ARRAY_OPEN = '[';
const ARRAY_CLOSE = ']';

export function concat(...parts: string[]): string {
    return parts.join('');
}

    /**
     * Serialize any object to MCF format
     */
export function serialize(obj: any): string {
        if (obj === null || obj === undefined) return '';
        if (typeof obj === 'string') return escapeString(obj);
        if (typeof obj === 'number') return obj.toString();
        if (typeof obj === 'boolean') return obj ? '1' : '0';
        if (Array.isArray(obj)) {
            const content = obj.map(item => serialize(item)).join(DELIM_ARRAY);
            return concat(ARRAY_OPEN, content, ARRAY_CLOSE);
        }
        if (typeof obj === 'object') {
            const content = Object.entries(obj)
                .filter(([_, value]) => value !== null && value !== undefined)
                .map(([key, value]) => {
                    const serializedValue = serialize(value);
                    return concat(key, DELIM_KV, serializedValue);
                })
                .join(DELIM_MAIN);
            return concat(BRACKET_OPEN, content, BRACKET_CLOSE);
        }
        return String(obj);
    }

    /**
     * Deserialize MCF format to object
     */
export function deserialize(mcf: string): any {
        if (!mcf) return null;

        // Check if it's a simple value
        if (!mcf.includes(DELIM_MAIN) && !mcf.includes(DELIM_KV) &&
            !mcf.startsWith(BRACKET_OPEN) && !mcf.startsWith(ARRAY_OPEN)) {
            return parseValue(mcf);
        }

        // Handle bracketed objects
        if (mcf.startsWith(BRACKET_OPEN) && mcf.endsWith(BRACKET_CLOSE)) {
            const content = mcf.substring(1, mcf.length - 1);
            if (!content) return {};

            const result: any = {};
            const pairs = splitRespectingEscapes(content, DELIM_MAIN);

            for (const pair of pairs) {
                const colonIndex = pair.indexOf(DELIM_KV);
                if (colonIndex === -1) continue;

                const key = pair.substring(0, colonIndex);
                const valueStr = pair.substring(colonIndex + 1);

                result[key] = parseValue(valueStr);
            }

            return result;
        }

        // Handle legacy format without brackets
        const result: any = {};
        const pairs = splitRespectingEscapes(mcf, DELIM_MAIN);

        for (const pair of pairs) {
            const colonIndex = pair.indexOf(DELIM_KV);
            if (colonIndex === -1) continue;

            const key = pair.substring(0, colonIndex);
            const valueStr = pair.substring(colonIndex + 1);

            result[key] = parseValue(valueStr);
        }

        return result;
    }

export function parseValue(value: string): any {
        if (!value) return null;

        // Check for bracketed object
        if (value.startsWith(BRACKET_OPEN) && value.endsWith(BRACKET_CLOSE)) {
            return deserialize(value);
        }

        // Check for bracketed array
        if (value.startsWith(ARRAY_OPEN) && value.endsWith(ARRAY_CLOSE)) {
            const content = value.substring(1, value.length - 1);
            if (!content) return [];
            return splitRespectingEscapes(content, DELIM_ARRAY)
                .map(item => parseValue(item));
        }


        // Parse primitive values
        if (value === '1') return true;
        if (value === '0') return false;

        const num = Number(value);
        if (!isNaN(num) && isFinite(num)) return num;

        return unescapeString(value);
    }
export function escapeString(str: string): string {
        return str
            .replace(/\\/g, '\\\\')
            .replace(/\|/g, '\\|')
            .replace(/=/g, '\\=')
            .replace(/;/g, '\\;')
            .replace(/\{/g, '\\{')
            .replace(/\}/g, '\\}')
            .replace(/\[/g, '\\[')
            .replace(/\]/g, '\\]');
    }

export function unescapeString(str: string): string {
        return str
            .replace(/\\\]/g, ']')
            .replace(/\\\[/g, '[')
            .replace(/\\}/g, '}')
            .replace(/\\{/g, '{')
            .replace(/\\;/g, ';')
            .replace(/\\=/g, '=')
            .replace(/\\\|/g, '|')
            .replace(/\\\\/g, '\\');
    }

export function splitRespectingEscapes(str: string, delimiter: string): string[] {
        const result: string[] = [];
        let current = '';
        let escaped = false;
        
        for (let i = 0; i < str.length; i++) {
            const char = str[i];
            
            if (escaped) {
                current += char;
                escaped = false;
            } else if (char === DELIM_ESCAPE) {
                current += char;
                escaped = true;
            } else if (char === delimiter) {
                result.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        
        if (current) result.push(current);
        return result;
    }



