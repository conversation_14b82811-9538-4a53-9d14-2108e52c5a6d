#include "mcf_parser.h"

mcf_object_t* mcf_parse(const char* mcf_string) {
    if (!mcf_string) return NULL;
    
    mcf_object_t* obj = malloc(sizeof(mcf_object_t));
    obj->pairs = malloc(sizeof(mcf_pair_t) * 16);
    obj->count = 0;
    obj->capacity = 16;
    
    char* str_copy = strdup(mcf_string);
    char* content = str_copy;
    
    // Handle bracketed objects
    if (content[0] == '{' && content[strlen(content) - 1] == '}') {
        content[strlen(content) - 1] = '\0'; // Remove closing bracket
        content++; // Skip opening bracket
    }
    
    char* token = strtok(content, ";");
    
    while (token != NULL) {
        char* colon = strchr(token, ':');
        if (colon != NULL) {
            *colon = '\0';
            
            if (obj->count >= obj->capacity) {
                obj->capacity *= 2;
                obj->pairs = realloc(obj->pairs, sizeof(mcf_pair_t) * obj->capacity);
            }
            
            obj->pairs[obj->count].key = strdup(token);
            obj->pairs[obj->count].value = strdup(colon + 1);
            obj->count++;
        }
        token = strtok(NULL, ";");
    }
    
    free(str_copy);
    return obj;
}

const char* mcf_get(mcf_object_t* obj, const char* key) {
    if (!obj || !key) return NULL;
    
    for (int i = 0; i < obj->count; i++) {
        if (strcmp(obj->pairs[i].key, key) == 0) {
            return obj->pairs[i].value;
        }
    }
    return NULL;
}

int mcf_get_int(mcf_object_t* obj, const char* key, int default_value) {
    const char* value = mcf_get(obj, key);
    return value ? atoi(value) : default_value;
}

int mcf_get_bool(mcf_object_t* obj, const char* key, int default_value) {
    const char* value = mcf_get(obj, key);
    if (!value) return default_value;
    return (strcmp(value, "1") == 0) ? 1 : 0;
}

void mcf_free(mcf_object_t* obj) {
    if (!obj) return;
    
    for (int i = 0; i < obj->count; i++) {
        free(obj->pairs[i].key);
        free(obj->pairs[i].value);
    }
    free(obj->pairs);
    free(obj);
}
