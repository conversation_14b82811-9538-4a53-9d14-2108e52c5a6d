{"name": "@gulpjs/messages", "version": "1.1.0", "description": "Symbols for all messages within gulp-cli", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "repository": "gulpjs/messages", "license": "MIT", "engines": {"node": ">=10.13.0"}, "main": "index.js", "files": ["index.js", "LICENSE"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {}, "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "eslint-plugin-node": "^11.1.0", "expect": "^27.0.0", "mocha": "^8.0.0", "nyc": "^15.0.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "publishConfig": {"access": "public"}, "keywords": []}