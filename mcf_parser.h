#ifndef MCF_PARSER_H
#define MCF_PARSER_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef struct {
    char* key;
    char* value;
} mcf_pair_t;

typedef struct {
    mcf_pair_t* pairs;
    int count;
    int capacity;
} mcf_object_t;

// Parse MCF string into key-value pairs (handles bracketed objects)
mcf_object_t* mcf_parse(const char* mcf_string);

// Get value by key (returns NULL if not found)
const char* mcf_get(mcf_object_t* obj, const char* key);

// Get integer value by key
int mcf_get_int(mcf_object_t* obj, const char* key, int default_value);

// Get boolean value by key (1 or 0)
int mcf_get_bool(mcf_object_t* obj, const char* key, int default_value);

// Free MCF object
void mcf_free(mcf_object_t* obj);

#endif // MCF_PARSER_H
