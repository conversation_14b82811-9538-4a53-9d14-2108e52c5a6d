import { exec } from "node:child_process";
import gulp, { series } from "gulp"
gulp.task("bind", (done)=>{
    exec("tsBind.exe", (error) => {
        if (error) {
            console.error(error);
            return done(error);
        }
        done()
    });
})
gulp.task("ts", (done)=>{
    exec("npx tsc", (error) => {
        if (error) {
            console.error(error);
            return done(error);
        }
        done()
    });
})
gulp.task("node", (done)=>{
    exec("node dist/main.js", (error,) => {
        if (error) {
            console.error(error);
            return done(error);
        }
        done();
    });
})
gulp.task("data", gulp.series("bind", "ts", "node"));
const Ccommand = "gcc.exe src/main/main.c src/main/mcf_parser.c -Isrc/main -o dist/main -lglfw3 -lopengl32 -lgdi32";
gulp.task("compile", (done) => {
    exec(Ccommand, (error) => {
        if (error) {
            console.error(error);
            return done(error);
        }
        done();
    });
});
gulp.task("default", gulp.parallel("compile", "data"))