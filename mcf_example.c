#include "mcf_parser.h"

int main() {
    // Example MCF data from your game
    const char* mcf_data = "{name:diamond_sword;properties:{fire_resistant:0,max_stack:1,tier:3}}";
    
    // Parse the MCF data
    mcf_object_t* obj = mcf_parse(mcf_data);
    if (!obj) {
        printf("Failed to parse MCF data\n");
        return 1;
    }
    
    // Access values
    const char* name = mcf_get(obj, "name");
    
    printf("Item: %s\n", name ? name : "unknown");
    printf("Parsed %d key-value pairs\n", obj->count);
    
    // Print all parsed pairs
    for (int i = 0; i < obj->count; i++) {
        printf("  %s: %s\n", obj->pairs[i].key, obj->pairs[i].value);
    }
    
    // Clean up
    mcf_free(obj);
    return 0;
}
