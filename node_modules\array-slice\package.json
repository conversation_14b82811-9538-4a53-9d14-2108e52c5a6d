{"name": "array-slice", "description": "Array-slice method. Slices `array` from the `start` index up to, but not including, the `end` index.", "version": "1.1.0", "homepage": "https://github.com/jonschlinkert/array-slice", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/array-slice", "bugs": {"url": "https://github.com/jonschlinkert/array-slice/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"gulp-format-md": "^1.0.0", "mocha": "^3.5.3"}, "keywords": ["array", "javascript", "js", "slice", "util", "utils"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-flatten", "array-unique", "array-xor"]}, "lint": {"reflinks": true}}}