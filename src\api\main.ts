import * as core from "./core.ts";
import * as fs from "node:fs";
import * as mcf from "./mcf.ts";

(async ()=>{
    let ou = await core.RegistryFunc.run();
    let data = {...ou, ...core.toGo};

    // Output JSON format
    let jsonOut:string = JSON.stringify(data);
    fs.writeFileSync("dist/out.json", jsonOut);

    // Output MCF format
    let mcfOut:string = mcf.MCFSerializer.serialize(data);
    fs.writeFileSync("dist/out.mcf", mcfOut);

    // Output comparison stats
    const jsonSize = jsonOut.length;
    const mcfSize = mcfOut.length;
    const reduction = ((jsonSize - mcfSize) / jsonSize * 100).toFixed(1);

    console.log(`Generated output files:`);
    console.log(`- JSON: dist/out.json (${jsonSize} bytes)`);
    console.log(`- MCF:  dist/out.mcf (${mcfSize} bytes)`);
    console.log(`- Size reduction: ${reduction}%`);
})();