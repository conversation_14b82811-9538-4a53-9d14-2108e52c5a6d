import * as core from "./core.ts";
import * as fs from "node:fs";
import { MCFSerializer } from "./mcf.ts";

(async ()=>{
    let ou = await core.RegistryFunc.run();
    let data = {...ou, ...core.toGo};

    // Output JSON format
    let jsonOut:string = JSON.stringify(data);
    fs.writeFileSync("dist/out.json", jsonOut);

    // Output MCF format
    let mcfOut:string = MCFSerializer.serialize(data);
    fs.writeFileSync("dist/out.mcf", mcfOut);

    // Output comparison stats
    const jsonSize = jsonOut.length;
    const mcfSize = mcfOut.length;
    const reduction = ((jsonSize - mcfSize) / jsonSize * 100).toFixed(1);

    const stats = `MCF Format Statistics:
JSON size: ${jsonSize} bytes
MCF size: ${mcfSize} bytes
Size reduction: ${reduction}%
Compression ratio: ${(jsonSize / mcfSize).toFixed(2)}:1
`;

    fs.writeFileSync("dist/format-stats.txt", stats);
    console.log(stats);
})();