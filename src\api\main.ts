import * as core from "./core.ts";
import * as fs from "node:fs";
import * as mcf from "./mcf.ts";

(async ()=>{
    let ou = await core.RegistryFunc.run();
    let data = {...ou, ...core.toGo};

    // Output JSON format
    let jsonOut:string = JSON.stringify(data);
    fs.writeFileSync("dist/out.json", jsonOut);

    // Output MCF format
    let mcfOut:string = mcf.serialize(data);
    fs.writeFileSync("dist/out.mcf", mcfOut);
})();