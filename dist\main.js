//@tsBind-Generated
import * as fs from 'node:fs';
//@tsBind-require ./api.ts
// ===== src/api/mcf.ts =====
var api;
(function (api) {
    var mcf;
    (function (mcf_1) {
        /**
         * MCF (MineCraft Format) - A size-efficient, human-readable, C-compatible serialization format
         *
         * Format Specification:
         * - Uses pipe (|) as primary delimiter
         * - Uses colon (:) for key-value pairs
         * - Uses semicolon (;) for array elements
         * - Uses short field names to reduce size
         * - Numbers are stored as-is (no quotes)
         * - Booleans as 1/0
         * - Strings are unquoted unless they contain delimiters
         *
         * Example:
         * JSON: {"name":"oak_planks","properties":{"fire_resistant":false,"max_stack":64}}
         * MCF:  n:oak_planks|p:fr:0;ms:64
         */
        class MCFSerializer {
            static DELIM_MAIN = ';';
            static DELIM_KV = ':';
            static DELIM_ARRAY = ',';
            static DELIM_ESCAPE = '|';
            static BRACKET_OPEN = '{';
            static BRACKET_CLOSE = '}';
            static concat(...parts) {
                return parts.join('');
            }
            /**
             * Serialize any object to MCF format
             */
            static serialize(obj) {
                if (obj === null || obj === undefined)
                    return '';
                if (typeof obj === 'string')
                    return MCFSerializer.escapeString(obj);
                if (typeof obj === 'number')
                    return obj.toString();
                if (typeof obj === 'boolean')
                    return obj ? '1' : '0';
                if (Array.isArray(obj)) {
                    return obj.map(item => MCFSerializer.serialize(item)).join(MCFSerializer.DELIM_ARRAY);
                }
                if (typeof obj === 'object') {
                    const content = Object.entries(obj)
                        .filter(([_, value]) => value !== null && value !== undefined)
                        .map(([key, value]) => {
                        const serializedValue = MCFSerializer.serialize(value);
                        return MCFSerializer.concat(key, MCFSerializer.DELIM_KV, serializedValue);
                    })
                        .join(MCFSerializer.DELIM_MAIN);
                    return MCFSerializer.concat(MCFSerializer.BRACKET_OPEN, content, MCFSerializer.BRACKET_CLOSE);
                }
                return String(obj);
            }
            /**
             * Deserialize MCF format to object
             */
            static deserialize(mcf) {
                if (!mcf)
                    return null;
                // Check if it's a simple value
                if (!mcf.includes(MCFSerializer.DELIM_MAIN) && !mcf.includes(MCFSerializer.DELIM_KV) &&
                    !mcf.startsWith(MCFSerializer.BRACKET_OPEN)) {
                    return MCFSerializer.parseValue(mcf);
                }
                // Handle bracketed objects
                if (mcf.startsWith(MCFSerializer.BRACKET_OPEN) && mcf.endsWith(MCFSerializer.BRACKET_CLOSE)) {
                    const content = mcf.substring(1, mcf.length - 1);
                    if (!content)
                        return {};
                    const result = {};
                    const pairs = MCFSerializer.splitRespectingEscapes(content, MCFSerializer.DELIM_MAIN);
                    for (const pair of pairs) {
                        const colonIndex = pair.indexOf(MCFSerializer.DELIM_KV);
                        if (colonIndex === -1)
                            continue;
                        const key = pair.substring(0, colonIndex);
                        const valueStr = pair.substring(colonIndex + 1);
                        result[key] = MCFSerializer.parseValue(valueStr);
                    }
                    return result;
                }
                // Handle legacy format without brackets
                const result = {};
                const pairs = MCFSerializer.splitRespectingEscapes(mcf, MCFSerializer.DELIM_MAIN);
                for (const pair of pairs) {
                    const colonIndex = pair.indexOf(MCFSerializer.DELIM_KV);
                    if (colonIndex === -1)
                        continue;
                    const key = pair.substring(0, colonIndex);
                    const valueStr = pair.substring(colonIndex + 1);
                    result[key] = MCFSerializer.parseValue(valueStr);
                }
                return result;
            }
            static parseValue(value) {
                if (!value)
                    return null;
                // Check for bracketed object
                if (value.startsWith(MCFSerializer.BRACKET_OPEN) && value.endsWith(MCFSerializer.BRACKET_CLOSE)) {
                    return MCFSerializer.deserialize(value);
                }
                // Check for array
                if (value.includes(MCFSerializer.DELIM_ARRAY)) {
                    return MCFSerializer.splitRespectingEscapes(value, MCFSerializer.DELIM_ARRAY)
                        .map(item => MCFSerializer.parseValue(item));
                }
                // Check for nested object (legacy format)
                if (value.includes(MCFSerializer.DELIM_MAIN)) {
                    return MCFSerializer.deserialize(value);
                }
                // Parse primitive values
                if (value === '1')
                    return true;
                if (value === '0')
                    return false;
                const num = Number(value);
                if (!isNaN(num) && isFinite(num))
                    return num;
                return MCFSerializer.unescapeString(value);
            }
            static escapeString(str) {
                return str
                    .replace(/\\/g, '\\\\')
                    .replace(/\|/g, '\\|')
                    .replace(/:/g, '\\:')
                    .replace(/;/g, '\\;')
                    .replace(/\{/g, '\\{')
                    .replace(/\}/g, '\\}');
            }
            static unescapeString(str) {
                return str
                    .replace(/\\}/g, '}')
                    .replace(/\\{/g, '{')
                    .replace(/\\;/g, ';')
                    .replace(/\\:/g, ':')
                    .replace(/\\\|/g, '|')
                    .replace(/\\\\/g, '\\');
            }
            static splitRespectingEscapes(str, delimiter) {
                const result = [];
                let current = '';
                let escaped = false;
                for (let i = 0; i < str.length; i++) {
                    const char = str[i];
                    if (escaped) {
                        current += char;
                        escaped = false;
                    }
                    else if (char === MCFSerializer.DELIM_ESCAPE) {
                        current += char;
                        escaped = true;
                    }
                    else if (char === delimiter) {
                        result.push(current);
                        current = '';
                    }
                    else {
                        current += char;
                    }
                }
                if (current)
                    result.push(current);
                return result;
            }
        }
        mcf_1.MCFSerializer = MCFSerializer;
    })(mcf = api.mcf || (api.mcf = {}));
})(api || (api = {}));
// ===== src/api/core.ts =====
(function (api) {
    var core;
    (function (core) {
        core.toGo = {};
        function send(name, what) {
            core.toGo[name] = what;
        }
        core.send = send;
        class gameComponet {
            static empty;
            registryName = "";
            name = "";
        }
        core.gameComponet = gameComponet;
        class X extends gameComponet {
            ToJSON() {
                return { name: this.name };
            }
            ToMCF() {
                return `n:.{this.name}`;
            }
        }
        function key(a) {
            return a.join(":");
        }
        core.key = key;
        class RegistryFunc {
            func;
            static all = [];
            constructor(func, priority) {
                this.func = func;
                if (RegistryFunc.all[priority] == undefined) {
                    RegistryFunc.all[priority] = [];
                }
                RegistryFunc.all[priority].push(func);
            }
            static async run() {
                let promised = [];
                RegistryFunc.all.forEach(async (value) => {
                    value.forEach((value) => {
                        promised.push(value());
                    });
                    await Promise.all(promised);
                    promised = [];
                });
                await Promise.all(promised);
                let all = api.registries.registry.all;
                let ou = {};
                Object.keys(all).forEach((val) => {
                    all[val].forEach((value) => {
                        let a = value.toArray();
                        if (Array.isArray(a)) {
                            a.forEach((value) => {
                                if (ou[val] == undefined) {
                                    ou[val] = [];
                                }
                                ou[val].push(value);
                            });
                        }
                        else {
                            Object.keys(a).forEach((value) => {
                                a[value].forEach((v) => {
                                    if (ou[val] == undefined) {
                                        ou[val] = {};
                                    }
                                    if (ou[val][value] == undefined) {
                                        ou[val][value] = [];
                                    }
                                    ou[val][value].push(v);
                                });
                            });
                        }
                    });
                });
                return ou;
            }
        }
        core.RegistryFunc = RegistryFunc;
        gameComponet.empty = new X();
    })(core = api.core || (api.core = {}));
})(api || (api = {}));
// ===== src/api/registries.ts =====
(function (api) {
    var registries;
    (function (registries) {
        class registry {
            type;
            space;
            static all = {};
            registry = {};
            constructor(type, space) {
                this.type = type;
                this.space = space;
                if (registry.all[this.type] == undefined) {
                    registry.all[this.type] = [];
                }
                registry.all[this.type].push(this);
            }
            get(what) {
                return this.registry[api.core.key([this.space, what])];
            }
            add(v) {
                let value = v;
                //@ts-ignore
                this.registry[api.core.key([this.space, value.name])] = value;
                value.registryName = api.core.key([this.space, value.name]);
                return value;
            }
            toArray() {
                let a = [];
                Array.from(Object.keys(this.registry)).forEach((value, index) => {
                    a[index] = this.registry[value].ToJSON();
                    a[index].name = this.space + ":" + a[index].name;
                });
                return a;
            }
        }
        registries.registry = registry;
    })(registries = api.registries || (api.registries = {}));
})(api || (api = {}));
// ===== src/api/block.ts =====
(function (api) {
    var block;
    (function (block_1) {
        class blockProperties {
            flammable = false;
            Flamable() {
                this.flammable = true;
                return this;
            }
            spreadChance = 0;
            SpreadChance(a) {
                this.spreadChance = a;
                return this;
            }
            burnTime = 0;
            BurnTime(a) {
                this.burnTime = a;
                return this;
            }
            hitbox = [[[0, 0, 0], [16, 16, 16]]];
            Hitbox(a) {
                this.hitbox = a;
                return this;
            }
            hardness = 1;
            Hardness(a) {
                this.hardness = a;
                return this;
            }
            resistance = 1;
            Resistance(a) {
                this.resistance = a;
                return this;
            }
            friction = 0.6;
            Friction(a) {
                this.friction = a;
                return this;
            }
            luminance = 0;
            Luminance(a) {
                this.luminance = a;
                return this;
            }
            transparent = false;
            Transparent(a) {
                this.transparent = a;
                return this;
            }
            mineableWith = "";
            MineableWith(a) {
                this.mineableWith = a;
                return this;
            }
        }
        block_1.blockProperties = blockProperties;
        class block extends api.core.gameComponet {
            behaviour;
            properties;
            attachedComponents;
            static GUI = { name: "gui" };
            static block = { name: "block" };
            static pillarBlock = { name: "pillar_block" };
            ToJSON() {
                let attachedComponents = [];
                this.attachedComponents.forEach((value) => {
                    if (value == null) {
                        return;
                    }
                    attachedComponents.push(value.registryName);
                });
                return {
                    properties: this.properties,
                    name: this.name,
                    attachedComponents: attachedComponents
                };
            }
            ToMCF() {
                let attachedComponents = [];
                this.attachedComponents.forEach((value) => {
                    if (value == null) {
                        return;
                    }
                    attachedComponents.push(value.registryName);
                });
                const data = {
                    properties: this.properties,
                    name: this.name,
                    attachedComponents: attachedComponents
                };
                return api.mcf.MCFSerializer.serialize(data);
            }
            constructor(name, behaviour, properties, attachedComponents) {
                super();
                this.behaviour = behaviour;
                this.properties = properties;
                this.attachedComponents = attachedComponents;
                this.name = name;
                this.registryName = name;
            }
        }
        block_1.block = block;
        class blockRegistry extends api.registries.registry {
            constructor(space) {
                super("block", space);
            }
        }
        block_1.blockRegistry = blockRegistry;
        block_1.f = new api.core.RegistryFunc(async () => {
            console.log("block");
            let blockReg = new blockRegistry("_");
            block_1.airBlock = blockReg.add(new block("_", block.block, new blockProperties().Hitbox([[[0, 0, 0], [0, 0, 0]]]), [null]));
            block_1.bedrockBlock = blockReg.add(new block("bedrock", block.block, new blockProperties().Resistance(-1).Hardness(-1), [null]));
            return null;
        }, 0);
    })(block = api.block || (api.block = {}));
})(api || (api = {}));
// ===== src/api/item.ts =====
(function (api) {
    var item;
    (function (item) {
        class ItemProperties {
            fire_resistant = false;
            FireResistant() {
                this.fire_resistant = true;
                return this;
            }
            max_stack = 64;
            MaxStack(a) {
                this.max_stack = a;
                return this;
            }
            teir = 0;
            Teir(a) {
                this.teir = a;
                return this;
            }
            durability = 0;
            Durability(a) {
                this.durability = a;
                this.max_stack = 1;
                return this;
            }
            modifiera = 1;
            ModifierA(a) {
                this.modifiera = a;
                return this;
            }
            modifierb = 1;
            ModifierB(a) {
                this.modifierb = a;
                return this;
            }
        }
        item.ItemProperties = ItemProperties;
        class Item extends api.core.gameComponet {
            behaviour;
            static Item = { name: "item" };
            static BlockItem = { name: "block_item" };
            static Pickaxe = { name: "pickaxe" };
            properties;
            attachedComponents;
            constructor(name, behaviour, properties, attachedComponents) {
                super();
                this.behaviour = behaviour;
                if (attachedComponents[0] == null) {
                    attachedComponents = [api.core.gameComponet.empty];
                }
                this.name = name;
                this.registryName = name;
                this.properties = properties;
                this.attachedComponents = attachedComponents;
            }
            ToJSON() {
                let attachedComponents = [];
                this.attachedComponents.forEach((value) => {
                    if (value == null) {
                        return;
                    }
                    attachedComponents.push(value.registryName);
                });
                return {
                    name: this.name,
                    properties: this.properties,
                    behaviour: this.behaviour,
                    attachedComponents: attachedComponents
                };
            }
            ToMCF() {
                let attachedComponents = [];
                this.attachedComponents.forEach((value) => {
                    if (value == null) {
                        return;
                    }
                    attachedComponents.push(value.registryName);
                });
                const data = {
                    name: this.name,
                    properties: this.properties,
                    behaviour: this.behaviour,
                    attachedComponents: attachedComponents
                };
                return api.mcf.MCFSerializer.serialize(data);
            }
        }
        item.Item = Item;
        class ItemRegistry extends api.registries.registry {
            constructor(space) {
                super("item", space);
            }
        }
        item.ItemRegistry = ItemRegistry;
        new api.core.RegistryFunc(async () => {
            console.log("item");
            let itemRegistry = new ItemRegistry("_");
            item.bedrock = itemRegistry.add(new Item("bedrock", Item.BlockItem, new ItemProperties(), [api.block.bedrockBlock]));
            item.air = itemRegistry.add(new Item("_", Item.Item, new ItemProperties().MaxStack(1), [api.core.gameComponet.empty]));
            return null;
        }, 1);
    })(item = api.item || (api.item = {}));
})(api || (api = {}));
// ===== src/api/recipe.ts =====
(function (api) {
    var recipe;
    (function (recipe_1) {
        class recipeRegistery extends api.registries.registry {
            constructor(space) {
                super("recipe", space);
            }
            add(v) {
                if (this.registry[v[0].Rtype] == undefined) {
                    this.registry[v[0].Rtype] = [];
                }
                this.registry[v[0].Rtype].push(v[0]);
                return v;
            }
            toArray() {
                let a = {};
                let b = this.registry;
                Object.keys(b).forEach((value) => {
                    if (a[value] == undefined) {
                        a[value] = [];
                    }
                    b[value].forEach((value) => {
                        a[value.Rtype].push(value.ToJSON());
                    });
                });
                return a;
            }
        }
        recipe_1.recipeRegistery = recipeRegistery;
        class recipe extends api.core.gameComponet {
            name;
            data;
            constructor(name, data) {
                super();
                this.name = name;
                this.data = data;
                this.name = name;
                this.registryName = name;
            }
            static processData(data) {
                if (data instanceof api.core.gameComponet) {
                    return data.registryName;
                }
                else if (Array.isArray(data)) {
                    return data.map((value) => {
                        return this.processData(value);
                    });
                }
                else if (typeof data == "object") {
                    let a = Object.keys(data);
                    let b = {};
                    a.forEach((value) => {
                        b[value] = this.processData(data[value]);
                    });
                    return b;
                }
                else {
                    return data;
                }
            }
            ToJSON() {
                let dat = recipe.processData({ ...this.data });
                return {
                    name: this.name,
                    ...dat
                };
            }
            ToMCF() {
                let dat = recipe.processData({ ...this.data });
                const recipeData = {
                    name: this.name,
                    Rtype: this.Rtype,
                    ...dat
                };
                return api.mcf.MCFSerializer.serialize(recipeData);
            }
        }
        recipe_1.recipe = recipe;
    })(recipe = api.recipe || (api.recipe = {}));
})(api || (api = {}));
// ===== minecraft/data/dyeColour.ts =====
var minecraft;
(function (minecraft) {
    var data;
    (function (data) {
        var dyeColour;
        (function (dyeColour) {
            dyeColour.DyeColor = {
                red: ["#ff0000", "R"],
                yellow: ["#ffff00", "Y"],
                blue: ["#0000ff", "B"],
                orange: ["#cc8800", "RY"],
                purple: ["#880088", "RB"],
                green: ["#008800", "YB"],
                white: ["#eeeeee", "W"],
                black: ["#111111", "K"],
                pink: ["#ff55cc", "RW"],
                l_blue: ["#9999ff", "BW"],
                salmon: ["#ffaa77", "WWRY"],
                magenta: ["#ff00ff", "WRB"],
                lime: ["#00ff00", "WWYB"],
                brown: ["#663300", "RYB"],
                tan: ["#996644", "WWWRYB"],
                marroon: ["#662222", "RRRRYBKKK"],
                navy: ["#000033", "BKK"],
                deep_blue: ["#000077", "BK"],
                forest: ["#004400", "KKYB"],
                d_forest: ["#002200", "YBKK"],
                gray: ["#666666", "WK"],
                l_gray: ["#aaaaaa", "WWWK"],
                d_gray: ["#333333", "WKKK"],
                puce: ["#446600", "WYBYBRYB"],
                cyan: ["#00ffcc", "WWBBBY"],
                lavender: ["#ff88ff", "WWRB"],
                rose: ["#ffbbbb", "RWWW"],
                dusky_rose: ["#775555", "RWK"],
                lemon: ["#ccff33", "YYYB"],
                mint: ["#33ff99", "YBBBWW"],
                sand: ["#ddaa88", "WWWWWWRYB"],
                scarlet: ["#aa0000", "RRK"],
                cream: ["#ffffcc", "W"],
                duck_blue: ["#2299cc", "BKK"],
                mid_blue: ["#0066cc", "BK"],
                crimson: ["#660000", "RRK"],
                burgundy: ["#4f0e16", "RYB"],
                plum: ["#440044", "RBRY"],
                beige: ["#aaaa88", "WY"],
                hot_pink: ["#ff0088", "WWRYB"],
                olive: ["#556b2f", "RYBY"],
                eggplant: ["#330044", "RBK"],
                coffee: ["#443322", "RKK"],
                steel: ["#777799", "WWKKB"],
                dark_brown: ["#411f0f", "RYK"],
                jade: ["#006633", "YYB"],
                mustard: ["#d2b200", "WWRYK"],
                d_yellow: ["#776600", "YK"]
            };
            api.core.send("dye", dyeColour.DyeColor);
        })(dyeColour = data.dyeColour || (data.dyeColour = {}));
    })(data = minecraft.data || (minecraft.data = {}));
})(minecraft || (minecraft = {}));
// ===== minecraft/data/blocks.ts =====
(function (minecraft) {
    var data;
    (function (data) {
        var blocks;
        (function (blocks) {
            var Block = api.block;
            let blockReg = new Block.blockRegistry("minecraft");
            const logBlock = new Block.blockProperties().Flamable().BurnTime(500).SpreadChance(0.01).Hardness(3);
            const plankBlock = logBlock.Hardness(1);
            blocks.dirt = blockReg.add(new Block.block("dirt", Block.block.block, new Block.blockProperties().MineableWith("S"), [null]));
            blocks.oak_log = blockReg.add(new Block.block("oak_log", Block.block.pillarBlock, logBlock, [null]));
            blocks.oak_plank = blockReg.add(new Block.block("oak_plank", Block.block.block, plankBlock, [null]));
            blocks.stone = blockReg.add(new Block.block("stone", Block.block.block, new Block.blockProperties().MineableWith("P").Hardness(8).Resistance(1.5), [null]));
        })(blocks = data.blocks || (data.blocks = {}));
    })(data = minecraft.data || (minecraft.data = {}));
})(minecraft || (minecraft = {}));
// ===== minecraft/data/items.ts =====
(function (minecraft) {
    var data;
    (function (data) {
        var items;
        (function (items) {
            var Item = api.item;
            const itemReg = new Item.ItemRegistry("minecraft");
            items.dirt = itemReg.add(new Item.Item("dirt", Item.Item.BlockItem, new Item.ItemProperties(), [data.blocks.dirt]));
            items.stick = itemReg.add(new Item.Item("stick", Item.Item.Item, new Item.ItemProperties(), [null]));
            items.ironIngot = itemReg.add(new Item.Item("iron_ingot", Item.Item.Item, new Item.ItemProperties(), [null]));
            items.ironPickaxe = itemReg.add(new Item.Item("iron_pickaxe", Item.Item.Pickaxe, new Item.ItemProperties().Durability(512).Teir(3).ModifierA(2), [null]));
            items.oakPlanks = itemReg.add(new Item.Item("oak_planks", Item.Item.BlockItem, new Item.ItemProperties(), [data.blocks.oak_plank]));
            items.oakLog = itemReg.add(new Item.Item("oak_log", Item.Item.BlockItem, new Item.ItemProperties(), [data.blocks.oak_log]));
        })(items = data.items || (data.items = {}));
    })(data = minecraft.data || (minecraft.data = {}));
})(minecraft || (minecraft = {}));
// ===== minecraft/data/recipes.ts =====
(function (minecraft) {
    var data;
    (function (data_1) {
        var recipes;
        (function (recipes) {
            var recipe = api.recipe.recipe;
            class craftingShapless extends recipe {
                Rtype = "crafting_shapeless";
                constructor(name, data) {
                    super(name, data);
                }
            }
            recipes.craftingShapless = craftingShapless;
            class craftingShaped extends recipe {
                Rtype = "crafting_shaped";
                constructor(name, data) {
                    super(name, data);
                }
            }
            recipes.craftingShaped = craftingShaped;
            const recipeReg = new api.recipe.recipeRegistery("minecraft");
            recipes.oak_planks = recipeReg.add([new craftingShapless("oak_planks", {
                    ingredients: [data_1.items.oakLog],
                    result: data_1.items.oakPlanks,
                    count: 4
                })]);
            recipes.stick = recipeReg.add([new craftingShaped("stick", {
                    shape: [
                        "S",
                        "S"
                    ],
                    key: {
                        "S": data_1.items.oakPlanks
                    },
                    result: data_1.items.stick,
                    count: 4
                })]);
        })(recipes = data_1.recipes || (data_1.recipes = {}));
    })(data = minecraft.data || (minecraft.data = {}));
})(minecraft || (minecraft = {}));
// ===== src/api/main.ts =====
(function (api) {
    var main;
    (function (main) {
        (async () => {
            let ou = await api.core.RegistryFunc.run();
            let data = { ...ou, ...api.core.toGo };
            // Output JSON format
            let jsonOut = JSON.stringify(data);
            fs.writeFileSync("dist/out.json", jsonOut);
            // Output MCF format
            let mcfOut = api.mcf.MCFSerializer.serialize(data);
            fs.writeFileSync("dist/out.mcf", mcfOut);
            // Output comparison stats
            const jsonSize = jsonOut.length;
            const mcfSize = mcfOut.length;
            const reduction = ((jsonSize - mcfSize) / jsonSize * 100).toFixed(1);
            console.log(`Generated output files:`);
            console.log(`- JSON: dist/out.json (.{jsonSize} bytes)`);
            console.log(`- MCF:  dist/out.mcf (.{mcfSize} bytes)`);
            console.log(`- Size reduction: .{reduction}%`);
        })();
    })(main = api.main || (api.main = {}));
})(api || (api = {}));
