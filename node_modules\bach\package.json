{"name": "bach", "version": "2.0.1", "description": "Compose your async functions with elegance.", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": "gulpjs/bach", "license": "MIT", "engines": {"node": ">=10.13.0"}, "main": "index.js", "files": ["index.js", "lib", "LICENSE"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"async-done": "^2.0.0", "async-settle": "^2.0.0", "now-and-later": "^3.0.0"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "expect": "^27.5.1", "mocha": "^8.4.0", "nyc": "^15.1.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["compose", "fluent", "composing", "continuation", "function composition", "functional", "async", "map", "series", "parallel", "extension", "tracing", "debug", "timing", "aop", "settle", "promises", "callbacks", "observables", "streams", "end", "completion", "complete", "finish", "done", "error handling"]}