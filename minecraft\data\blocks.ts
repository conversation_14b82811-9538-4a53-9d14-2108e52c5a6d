import * as api$block from "../../src/api/block.ts";
import Block = api$block

let blockReg = new Block.blockRegistry("minecraft");

const logBlock = new Block.blockProperties().Flamable().BurnTime(500).SpreadChance(0.01).Hardness(3)
const plankBlock = logBlock.Hardness(1)

export let dirt = blockReg.add(new Block.block("dirt",
    Block.block.block, new Block.blockProperties().MineableWith("S"),
[null]))
export let oak_log = blockReg.add(new Block.block("oak_log",
    Block.block.pillarBlock, logBlock,
[null]))
export let oak_plank = blockReg.add(new Block.block("oak_plank",
    Block.block.block, plankBlock, 
[null]))

export let stone = blockReg.add(new Block.block("stone", 
    Block.block.block, new Block.blockProperties().MineableWith("P").Hardness(8).Resistance(1.5),
[null]
))
