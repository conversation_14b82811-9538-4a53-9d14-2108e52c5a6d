import * as api$item from "../../src/api/item.ts";
import * as blocks from "./blocks.ts";
import Item = api$item
const itemReg = new Item.ItemRegistry("minecraft")
export let dirt = itemReg.add(new Item.Item("dirt",
    Item.Item.BlockItem, new Item.ItemProperties(),
[blocks.dirt]))
export let stick = itemReg.add(new Item.Item("stick",
    Item.Item.Item, new Item.ItemProperties(),
[null]))
export let ironIngot = itemReg.add(new Item.Item("iron_ingot",
    Item.Item.Item, new Item.ItemProperties(),
[null]))
export let ironPickaxe = itemReg.add(new Item.Item("iron_pickaxe",
    Item.Item.Pickaxe, new Item.ItemProperties().Durability(512).Teir(3).ModifierA(2),
[null]))
export let oakPlanks = itemReg.add(new Item.Item("oak_planks",
    Item.Item.BlockItem, new Item.ItemProperties(),
[blocks.oak_plank]))
export let oakLog = itemReg.add(new Item.Item("oak_log",
    Item.Item.BlockItem, new Item.ItemProperties(),
[blocks.oak_log]))