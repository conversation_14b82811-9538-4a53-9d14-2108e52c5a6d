{"include": ["./dist/temp/main.ts"], "exclude": ["./node_modules", "./node_modules/@types/three", "./node_modules/@types/webxr"], "compilerOptions": {"lib": ["ES2024", "ES2023", "ES2022", "ES2021", "ES2020", "ES2019", "ES2018", "ES2017", "ES2016", "ES2015"], "outDir": "./dist", "target": "ES2022", "module": "nodenext", "moduleResolution": "nodenext", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "skipLibCheck": true}}