import * as block from "./block.ts";
import * as core from "./core.ts";
import * as registries from "./registries.ts";
import * as mcf from "./mcf.ts";
export class ItemProperties{
    public fire_resistant:boolean = false;
    FireResistant(){
        this.fire_resistant = true;
        return this;
    }
    public max_stack:number = 64;
    MaxStack(a:number){
        this.max_stack = a;
        return this;
    }
    public teir = 0;
    Teir(a:number){
        this.teir = a;
        return this;
    }
    public durability = 0;
    Durability(a:number){
        this.durability = a;
        this.max_stack = 1;
        return this;
    }
    modifiera = 1;
    ModifierA(a:number){
        this.modifiera = a;
        return this;
    }
    modifierb = 1;
    ModifierB(a:number){
        this.modifierb = a;
        return this;
    }
}
export type ItemBehaviour = {name:string,IT?:null};
export type ItemJSON = {
    properties: core.NonFunctionProperties<ItemProperties>;
    name: string;
    behaviour: ItemBehaviour;
    attachedComponents: (string| null)[];
}
export type itemBehaviourProcesor<T extends ItemBehaviour> = 
    T extends {name:"block_item"} ? block.block<any> : 
    null|core.gameComponet<any>;
export namespace Item{
    export type Item = {name:"item"};
    export type BlockItem = {name:"block_item"};
    export type Pickaxe = {name:"pickaxe"};
}
export class Item<V extends ItemBehaviour> extends core.gameComponet<ItemJSON>{
    static readonly Item:ItemBehaviour = {name:"item"};
    static readonly BlockItem:ItemBehaviour = {name:"block_item"};
    static readonly Pickaxe:ItemBehaviour = {name:"pickaxe"};
    public properties: ItemProperties;
    public attachedComponents:itemBehaviourProcesor<ItemBehaviour>[];
    constructor(name:string, public behaviour:V, properties:ItemProperties, attachedComponents:itemBehaviourProcesor<V>[]){
        super();
        if(attachedComponents[0] == null){
            attachedComponents = [core.gameComponet.empty] as itemBehaviourProcesor<V>[];
        }
        this.name = name;
        this.registryName = name;
        this.properties = properties;
        this.attachedComponents = attachedComponents as core.gameComponet<any>[];
    }
    public ToJSON() {
        let attachedComponents:string[] = [];
        this.attachedComponents.forEach((value)=>{
            if(value == null){
                return;
            }
            attachedComponents.push(value.registryName);
        })
        return {
            name: this.name,
            properties: this.properties,
            behaviour: this.behaviour,
            attachedComponents: attachedComponents
        }
    }
}
export class ItemRegistry extends registries.registry<Item<any>, ItemJSON>{
    constructor(space:string){
        super("item", space);
    }
}
export let bedrock:Item<Item.BlockItem>;
export let air:Item<Item.BlockItem>;
new core.RegistryFunc(async ()=>{
    console.log("item")
    let itemRegistry: ItemRegistry = new ItemRegistry("_");
    bedrock = itemRegistry.add(new Item("bedrock", Item.BlockItem ,new ItemProperties(), [block.bedrockBlock]));  
    air = itemRegistry.add(new Item("_", Item.Item, new ItemProperties().MaxStack(1), [core.gameComponet.empty]));
    return null
}, 1);