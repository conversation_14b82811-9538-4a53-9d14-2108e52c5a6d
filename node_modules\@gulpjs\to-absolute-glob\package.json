{"name": "@gulpjs/to-absolute-glob", "version": "4.0.0", "description": "Make a glob pattern absolute, ensuring that negative globs and patterns with trailing slashes are correctly handled.", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "repository": "gulpjs/to-absolute-glob", "license": "MIT", "engines": {"node": ">=10.13.0"}, "main": "index.js", "files": ["LICENSE", "index.js"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha"}, "dependencies": {"is-negated-glob": "^1.0.0"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "eslint-plugin-node": "^11.1.0", "expect": "^27.4.2", "mocha": "^8.4.0", "nyc": "^15.1.0"}, "publishConfig": {"access": "public"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["absolute", "file", "filepath", "glob", "negate", "negative", "path", "pattern", "resolve", "to"]}