/**
 * MCF (MineCraft Format) - A size-efficient, human-readable, C-compatible serialization format
 *
 * Format Specification:
 * - Uses pipe (|) as primary delimiter
 * - Uses colon (:) for key-value pairs
 * - Uses semicolon (;) for array elements
 * - Uses short field names to reduce size
 * - Numbers are stored as-is (no quotes)
 * - Booleans as 1/0
 * - Strings are unquoted unless they contain delimiters
 *
 * Example:
 * JSON: {"name":"oak_planks","properties":{"fire_resistant":false,"max_stack":64}}
 * MCF:  n:oak_planks|p:fr:0;ms:64
 */
export class MCFSerializer {
    static DELIM_MAIN = ';';
    static DELIM_KV = ':';
    static DELIM_ARRAY = ',';
    static DELIM_ESCAPE = '\\';
    static BRACKET_OPEN = '{';
    static BRACKET_CLOSE = '}';
    /**
     * Serialize any object to MCF format
     */
    static serialize(obj) {
        if (obj === null || obj === undefined)
            return '';
        if (typeof obj === 'string')
            return MCFSerializer.escapeString(obj);
        if (typeof obj === 'number')
            return obj.toString();
        if (typeof obj === 'boolean')
            return obj ? '1' : '0';
        if (Array.isArray(obj)) {
            return obj.map(item => MCFSerializer.serialize(item)).join(MCFSerializer.DELIM_ARRAY);
        }
        if (typeof obj === 'object') {
            const content = Object.entries(obj)
                .filter(([_, value]) => value !== null && value !== undefined)
                .map(([key, value]) => {
                const serializedValue = MCFSerializer.serialize(value);
                return `${key}${MCFSerializer.DELIM_KV}${serializedValue}`;
            })
                .join(MCFSerializer.DELIM_MAIN);
            return `${MCFSerializer.BRACKET_OPEN}${content}${MCFSerializer.BRACKET_CLOSE}`;
        }
        return String(obj);
    }
    /**
     * Deserialize MCF format to object
     */
    static deserialize(mcf) {
        if (!mcf)
            return null;
        // Check if it's a simple value
        if (!mcf.includes(MCFSerializer.DELIM_MAIN) && !mcf.includes(MCFSerializer.DELIM_KV) &&
            !mcf.startsWith(MCFSerializer.BRACKET_OPEN)) {
            return MCFSerializer.parseValue(mcf);
        }
        // Handle bracketed objects
        if (mcf.startsWith(MCFSerializer.BRACKET_OPEN) && mcf.endsWith(MCFSerializer.BRACKET_CLOSE)) {
            const content = mcf.substring(1, mcf.length - 1);
            if (!content)
                return {};
            const result = {};
            const pairs = MCFSerializer.splitRespectingEscapes(content, MCFSerializer.DELIM_MAIN);
            for (const pair of pairs) {
                const colonIndex = pair.indexOf(MCFSerializer.DELIM_KV);
                if (colonIndex === -1)
                    continue;
                const key = pair.substring(0, colonIndex);
                const valueStr = pair.substring(colonIndex + 1);
                result[key] = MCFSerializer.parseValue(valueStr);
            }
            return result;
        }
        // Handle legacy format without brackets
        const result = {};
        const pairs = MCFSerializer.splitRespectingEscapes(mcf, MCFSerializer.DELIM_MAIN);
        for (const pair of pairs) {
            const colonIndex = pair.indexOf(MCFSerializer.DELIM_KV);
            if (colonIndex === -1)
                continue;
            const key = pair.substring(0, colonIndex);
            const valueStr = pair.substring(colonIndex + 1);
            result[key] = MCFSerializer.parseValue(valueStr);
        }
        return result;
    }
    static parseValue(value) {
        if (!value)
            return null;
        // Check for bracketed object
        if (value.startsWith(MCFSerializer.BRACKET_OPEN) && value.endsWith(MCFSerializer.BRACKET_CLOSE)) {
            return MCFSerializer.deserialize(value);
        }
        // Check for array
        if (value.includes(MCFSerializer.DELIM_ARRAY)) {
            return MCFSerializer.splitRespectingEscapes(value, MCFSerializer.DELIM_ARRAY)
                .map(item => MCFSerializer.parseValue(item));
        }
        // Check for nested object (legacy format)
        if (value.includes(MCFSerializer.DELIM_MAIN)) {
            return MCFSerializer.deserialize(value);
        }
        // Parse primitive values
        if (value === '1')
            return true;
        if (value === '0')
            return false;
        const num = Number(value);
        if (!isNaN(num) && isFinite(num))
            return num;
        return MCFSerializer.unescapeString(value);
    }
    static escapeString(str) {
        return str
            .replace(/\\/g, '\\\\')
            .replace(/\|/g, '\\|')
            .replace(/:/g, '\\:')
            .replace(/;/g, '\\;')
            .replace(/\{/g, '\\{')
            .replace(/\}/g, '\\}');
    }
    static unescapeString(str) {
        return str
            .replace(/\\}/g, '}')
            .replace(/\\{/g, '{')
            .replace(/\\;/g, ';')
            .replace(/\\:/g, ':')
            .replace(/\\\|/g, '|')
            .replace(/\\\\/g, '\\');
    }
    static splitRespectingEscapes(str, delimiter) {
        const result = [];
        let current = '';
        let escaped = false;
        for (let i = 0; i < str.length; i++) {
            const char = str[i];
            if (escaped) {
                current += char;
                escaped = false;
            }
            else if (char === MCFSerializer.DELIM_ESCAPE) {
                current += char;
                escaped = true;
            }
            else if (char === delimiter) {
                result.push(current);
                current = '';
            }
            else {
                current += char;
            }
        }
        if (current)
            result.push(current);
        return result;
    }
    /**
     * Get compression ratio compared to JSON
     */
    static getCompressionRatio(obj) {
        const json = JSON.stringify(obj);
        const mcf = MCFSerializer.serialize(obj);
        const ratio = (json.length - mcf.length) / json.length;
        return {
            json,
            mcf,
            ratio: Math.round(ratio * 100) / 100
        };
    }
}
/**
 * C-compatible parser header generation
 */
export class MCFCGenerator {
    static generateHeader() {
        return `
#ifndef MCF_PARSER_H
#define MCF_PARSER_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef struct {
    char* key;
    char* value;
} mcf_pair_t;

typedef struct {
    mcf_pair_t* pairs;
    int count;
    int capacity;
} mcf_object_t;

// Parse MCF string into key-value pairs (handles bracketed objects)
mcf_object_t* mcf_parse(const char* mcf_string);

// Get value by key (returns NULL if not found)
const char* mcf_get(mcf_object_t* obj, const char* key);

// Get integer value by key
int mcf_get_int(mcf_object_t* obj, const char* key, int default_value);

// Get boolean value by key (1 or 0)
int mcf_get_bool(mcf_object_t* obj, const char* key, int default_value);

// Free MCF object
void mcf_free(mcf_object_t* obj);

#endif // MCF_PARSER_H
        `.trim();
    }
    static generateImplementation() {
        return `
#include "mcf_parser.h"

mcf_object_t* mcf_parse(const char* mcf_string) {
    if (!mcf_string) return NULL;

    mcf_object_t* obj = malloc(sizeof(mcf_object_t));
    obj->pairs = malloc(sizeof(mcf_pair_t) * 16);
    obj->count = 0;
    obj->capacity = 16;

    char* str_copy = strdup(mcf_string);
    char* content = str_copy;

    // Handle bracketed objects
    if (content[0] == '{' && content[strlen(content) - 1] == '}') {
        content[strlen(content) - 1] = '\\0'; // Remove closing bracket
        content++; // Skip opening bracket
    }

    char* token = strtok(content, "|");

    while (token != NULL) {
        char* colon = strchr(token, ':');
        if (colon != NULL) {
            *colon = '\\0';

            if (obj->count >= obj->capacity) {
                obj->capacity *= 2;
                obj->pairs = realloc(obj->pairs, sizeof(mcf_pair_t) * obj->capacity);
            }

            obj->pairs[obj->count].key = strdup(token);
            obj->pairs[obj->count].value = strdup(colon + 1);
            obj->count++;
        }
        token = strtok(NULL, "|");
    }

    free(str_copy);
    return obj;
}

const char* mcf_get(mcf_object_t* obj, const char* key) {
    if (!obj || !key) return NULL;
    
    for (int i = 0; i < obj->count; i++) {
        if (strcmp(obj->pairs[i].key, key) == 0) {
            return obj->pairs[i].value;
        }
    }
    return NULL;
}

int mcf_get_int(mcf_object_t* obj, const char* key, int default_value) {
    const char* value = mcf_get(obj, key);
    return value ? atoi(value) : default_value;
}

int mcf_get_bool(mcf_object_t* obj, const char* key, int default_value) {
    const char* value = mcf_get(obj, key);
    if (!value) return default_value;
    return (strcmp(value, "1") == 0) ? 1 : 0;
}

void mcf_free(mcf_object_t* obj) {
    if (!obj) return;
    
    for (int i = 0; i < obj->count; i++) {
        free(obj->pairs[i].key);
        free(obj->pairs[i].value);
    }
    free(obj->pairs);
    free(obj);
}
        `.trim();
    }
}
